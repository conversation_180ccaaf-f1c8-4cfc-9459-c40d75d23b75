const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

const terminalThemes = [
  {
    name: 'Termius Dark',
    backgroundColor: '#1a1a1a',
    textColor: '#00ff88',
    isDefault: true
  },
  {
    name: 'Termius Light',
    backgroundColor: '#ffffff',
    textColor: '#333333',
    isDefault: false
  },
  {
    name: 'Kanagawa Wave',
    backgroundColor: '#1f1f28',
    textColor: '#dcd7ba',
    isDefault: false
  },
  {
    name: 'Kanagawa Dragon',
    backgroundColor: '#181616',
    textColor: '#c5c9c5',
    isDefault: false
  },
  {
    name: 'Kanagawa Lotus',
    backgroundColor: '#f2ecbc',
    textColor: '#545464',
    isDefault: false
  },
  {
    name: 'Hacker Blue',
    backgroundColor: '#0d1117',
    textColor: '#58a6ff',
    isDefault: false
  },
  {
    name: 'Ha<PERSON>',
    backgroundColor: '#0d1117',
    textColor: '#39d353',
    isDefault: false
  },
  {
    name: 'Hacker Red',
    backgroundColor: '#0d1117',
    textColor: '#f85149',
    isDefault: false
  },
  {
    name: 'Everforest Dark',
    backgroundColor: '#2d353b',
    textColor: '#d3c6aa',
    isDefault: false
  },
  {
    name: 'Everforest Light',
    backgroundColor: '#fdf6e3',
    textColor: '#5c6a72',
    isDefault: false
  },
  {
    name: 'Night Owl',
    backgroundColor: '#011627',
    textColor: '#d6deeb',
    isDefault: false
  },
  {
    name: 'Light Owl',
    backgroundColor: '#fbfbfb',
    textColor: '#403f53',
    isDefault: false
  },
  {
    name: 'Aura',
    backgroundColor: '#15141b',
    textColor: '#edecee',
    isDefault: false
  },
  {
    name: 'Matrix',
    backgroundColor: '#000000',
    textColor: '#00ff00',
    isDefault: false
  },
  {
    name: 'Cyberpunk',
    backgroundColor: '#0f0f23',
    textColor: '#ff00ff',
    isDefault: false
  },
  {
    name: 'Retro Amber',
    backgroundColor: '#1e1e1e',
    textColor: '#ffb000',
    isDefault: false
  },
  {
    name: 'Ocean Blue',
    backgroundColor: '#001122',
    textColor: '#4fc3f7',
    isDefault: false
  },
  {
    name: 'Forest Green',
    backgroundColor: '#0f1419',
    textColor: '#7cb342',
    isDefault: false
  }
]

async function seedTerminalThemes() {
  console.log('🎨 Populando temas do terminal...')

  try {
    // Verificar se já existem temas
    const existingThemes = await prisma.terminalTheme.count()
    
    if (existingThemes > 0) {
      console.log(`⚠️  Já existem ${existingThemes} temas no banco de dados.`)
      console.log('Pulando criação de temas padrão.')
      return
    }

    // Criar todos os temas
    for (const theme of terminalThemes) {
      await prisma.terminalTheme.create({
        data: theme
      })
      console.log(`✅ Tema criado: ${theme.name}`)
    }

    console.log(`🎉 ${terminalThemes.length} temas do terminal criados com sucesso!`)

  } catch (error) {
    console.error('❌ Erro ao criar temas do terminal:', error)
    throw error
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  seedTerminalThemes()
    .catch((error) => {
      console.error(error)
      process.exit(1)
    })
    .finally(async () => {
      await prisma.$disconnect()
    })
}

module.exports = { seedTerminalThemes }
