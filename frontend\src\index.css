@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .responsive-container {
    @apply w-full px-4 sm:px-6 lg:px-8 mx-auto;
  }

  .responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6;
  }

  .responsive-card {
    @apply p-4 sm:p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 transition-colors;
  }

  .responsive-button {
    @apply w-full sm:w-auto flex items-center justify-center gap-2 px-4 py-2 rounded-md;
  }

  .responsive-table-container {
    @apply overflow-x-auto -mx-4 sm:mx-0 shadow-sm ring-1 ring-black ring-opacity-5 dark:ring-gray-700 rounded-lg;
  }

  .responsive-heading {
    @apply text-xl sm:text-2xl font-bold;
  }

  .responsive-text {
    @apply text-sm sm:text-base;
  }

  .responsive-flex {
    @apply flex flex-col sm:flex-row sm:items-center gap-4;
  }

  /* Scrollbar customizado moderno */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }

  .dark .custom-scrollbar {
    scrollbar-color: rgba(75, 85, 99, 0.6) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.6);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.8);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(75, 85, 99, 0.9);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:active {
    background: rgba(156, 163, 175, 1);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:active {
    background: rgba(75, 85, 99, 1);
  }

  /* Scrollbar para áreas escuras (como o terminal) */
  .custom-scrollbar-dark {
    scrollbar-width: thin;
    scrollbar-color: rgba(75, 85, 99, 0.6) transparent;
  }

  .custom-scrollbar-dark::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar-dark::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  .custom-scrollbar-dark::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.6);
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .custom-scrollbar-dark::-webkit-scrollbar-thumb:hover {
    background: rgba(75, 85, 99, 0.9);
  }

  .custom-scrollbar-dark::-webkit-scrollbar-thumb:active {
    background: rgba(75, 85, 99, 1);
  }

  /* Scrollbar para lista de comandos - mais visível e moderno */
  .custom-scrollbar-commands {
    scrollbar-width: thin;
    scrollbar-color: rgba(99, 102, 241, 0.4) rgba(55, 65, 81, 0.1);
  }

  .dark .custom-scrollbar-commands {
    scrollbar-color: rgba(99, 102, 241, 0.6) rgba(75, 85, 99, 0.2);
  }

  .custom-scrollbar-commands::-webkit-scrollbar {
    width: 10px;
  }

  .custom-scrollbar-commands::-webkit-scrollbar-track {
    background: rgba(55, 65, 81, 0.1);
    border-radius: 5px;
    margin: 4px 0;
  }

  .dark .custom-scrollbar-commands::-webkit-scrollbar-track {
    background: rgba(75, 85, 99, 0.2);
  }

  .custom-scrollbar-commands::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(99, 102, 241, 0.6) 0%, rgba(79, 70, 229, 0.6) 100%);
    border-radius: 5px;
    border: 1px solid rgba(55, 65, 81, 0.2);
    transition: all 0.3s ease;
  }

  .dark .custom-scrollbar-commands::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(99, 102, 241, 0.7) 0%, rgba(79, 70, 229, 0.7) 100%);
    border: 1px solid rgba(75, 85, 99, 0.3);
  }

  .custom-scrollbar-commands::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(99, 102, 241, 0.8) 0%, rgba(79, 70, 229, 0.8) 100%);
    transform: scaleX(1.1);
  }

  .dark .custom-scrollbar-commands::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(99, 102, 241, 0.9) 0%, rgba(79, 70, 229, 0.9) 100%);
  }

  .custom-scrollbar-commands::-webkit-scrollbar-thumb:active {
    background: linear-gradient(180deg, rgba(99, 102, 241, 1) 0%, rgba(79, 70, 229, 1) 100%);
  }

  /* Scrollbar corner para quando há scroll horizontal e vertical */
  .custom-scrollbar::-webkit-scrollbar-corner,
  .custom-scrollbar-dark::-webkit-scrollbar-corner,
  .custom-scrollbar-commands::-webkit-scrollbar-corner {
    background: transparent;
  }


}