import { api } from '../lib/axios'
import { CreateUserDTO, UpdateUserDTO, User, UserResponse, LoginResponse } from '../types/user'

export const userService = {
  async create(data: CreateUserDTO) {
    const response = await api.post<UserResponse>('/api/users', data)
    return response.data
  },

  async login(email: string, password: string) {
    const response = await api.post<LoginResponse>('/login', { email, password })
    return response.data
  },

  async getProfile() {
    const response = await api.get<UserResponse>('/api/users/profile')
    return response.data
  },

  async updateProfile(data: { name?: string; password?: string }) {
    console.log('Serviço - Atualizando perfil próprio:', data)
    try {
      const response = await api.put<UserResponse>('/api/users/profile', data)
      console.log('Serviço - Resposta da atualização do perfil:', response.data)
      return response.data
    } catch (error) {
      console.error('Serviço - Erro na atualização do perfil:', error)
      throw error
    }
  },

  async getAll(includeInactive: boolean = false) {
    const response = await api.get<User[]>(`/api/users${includeInactive ? '?includeInactive=true' : ''}`)
    return response.data
  },

  async getById(id: string) {
    const response = await api.get<UserResponse>(`/api/users/${id}`)
    return response.data
  },

  async update(id: string, data: UpdateUserDTO) {
    console.log('Serviço - Atualizando usuário:', id, data)
    try {
      const response = await api.put<UserResponse>(`/api/users/${id}`, data)
      console.log('Serviço - Resposta da atualização:', response.data)
      return response.data
    } catch (error) {
      console.error('Serviço - Erro na atualização:', error)
      throw error
    }
  },

  async delete(id: string) {
    await api.delete(`/api/users/${id}`)
  },

  async deactivate(id: string) {
    const response = await api.patch<UserResponse>(`/api/users/${id}/deactivate`)
    return response.data
  },

  async reactivate(id: string) {
    const response = await api.patch<UserResponse>(`/api/users/${id}/reactivate`)
    return response.data
  }
} 