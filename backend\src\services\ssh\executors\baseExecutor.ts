import { Node<PERSON><PERSON> } from 'node-ssh';
import { CommandResult } from '../../../types/server';
import { Logger } from '../../../utils/logger';
import { LOOP_PROTECTION_CONFIG, calculateMaxCompletionChecks } from '../../../config/loopProtection';

/**
 * Interface para executores de comandos SSH
 */
export interface ICommandExecutor {
  /**
   * Executa um comando SSH
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  executeCommand(command: string): Promise<CommandResult>;

  /**
   * Executa um comando com múltiplas linhas
   * @param command Comando com múltiplas linhas
   * @returns Resultado do comando
   */
  executeMultilineCommand(command: string): Promise<CommandResult>;
}

/**
 * Classe base para executores de comandos SSH
 */
export abstract class BaseExecutor implements ICommandExecutor {
  protected ssh: NodeSSH;

  // Configurações padrão de timeout usando configuração centralizada
  protected BASE_TIMEOUT = LOOP_PROTECTION_CONFIG.TIMEOUTS.BASE_TIMEOUT_MS;
  protected TIMEOUT_PER_COMMAND = LOOP_PROTECTION_CONFIG.TIMEOUTS.TIMEOUT_PER_COMMAND_MS;
  protected MAX_TIMEOUT = LOOP_PROTECTION_CONFIG.TIMEOUTS.MAX_TIMEOUT_MS;

  constructor(ssh: NodeSSH) {
    this.ssh = ssh;
  }

  /**
   * Calcula o timeout dinâmico com base na quantidade de comandos
   * @param commandCount Número de comandos a serem executados
   * @returns Timeout em milissegundos
   */
  protected calculateDynamicTimeout(commandCount: number): number {
    // Calcular o timeout com base no número de comandos
    const calculatedTimeout = this.BASE_TIMEOUT + (commandCount * this.TIMEOUT_PER_COMMAND);

    // Limitar ao timeout máximo
    return Math.min(calculatedTimeout, this.MAX_TIMEOUT);
  }

  /**
   * Executa um comando SSH
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  async executeCommand(command: string): Promise<CommandResult> {
    try {
      // Limpar o comando para remover caracteres invisíveis e espaços extras
      const cleanCommand = command.trim().replace(/\s+/g, ' ');

      Logger.log(`Executando comando: ${cleanCommand.substring(0, 50)}${cleanCommand.length > 50 ? '...' : ''}`);

      // Verificar se o comando contém múltiplas linhas
      if (command.includes('\n')) {
        Logger.log('Detectado comando com múltiplas linhas, executando linha por linha');
        return await this.executeMultilineCommand(command);
      }

      // Para comandos simples
      Logger.log('Usando execCommand para comando simples');
      const result = await this.ssh.execCommand(cleanCommand, {
        cwd: '/',
        onStdout: (chunk) => {
          Logger.log(`stdout (${chunk.length} bytes): ${chunk.toString('utf8').substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);
        },
        onStderr: (chunk) => {
          Logger.error(`stderr (${chunk.length} bytes): ${chunk.toString('utf8').substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);
        }
      });

      return {
        stdout: result.stdout,
        stderr: result.stderr,
        code: result.code || 0,
      };
    } catch (error) {
      Logger.error('Erro ao executar comando:', error);
      throw new Error(`Falha ao executar comando: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Executa um comando com múltiplas linhas
   * @param command Comando com múltiplas linhas
   * @returns Resultado do comando
   */
  async executeMultilineCommand(command: string): Promise<CommandResult> {
    // Dividir o comando em linhas individuais
    const lines = command.split('\n').filter(line => line.trim() !== '');
    const commandCount = lines.length;

    // Calcular o timeout dinâmico com base na quantidade de comandos
    const dynamicTimeout = this.calculateDynamicTimeout(commandCount);

    Logger.log(`Executando ${commandCount} comandos separados com timeout dinâmico de ${dynamicTimeout}ms`);

    // Criar uma promise com timeout global para todos os comandos
    const timeoutPromise = new Promise<CommandResult>((resolve) => {
      setTimeout(() => {
        resolve({
          stdout: '',
          stderr: `[ERRO] Timeout global atingido após ${dynamicTimeout/1000} segundos para ${commandCount} comandos`,
          code: 124 // Código de timeout
        });
      }, dynamicTimeout);
    });

    // Promise para executar todos os comandos
    const executePromise = async (): Promise<CommandResult> => {
      let combinedOutput = '';
      let combinedError = '';
      let lastCode = 0;

      // Executar cada linha separadamente
      for (let i = 0; i < commandCount; i++) {
        const line = lines[i].trim();
        Logger.log(`Executando linha ${i+1}/${commandCount}: ${line}`);

        try {
          // Executar o comando individual
          const result = await this.executeCommand(line);

          // Adicionar a saída à saída combinada
          combinedOutput += `\n--- Comando: ${line} ---\n${result.stdout}\n`;

          // Se houver erro, adicionar ao erro combinado
          if (result.stderr) {
            combinedError += `\n--- Erro no comando: ${line} ---\n${result.stderr}\n`;
          }

          // Atualizar o código de saída
          if (result.code !== 0 && result.code !== undefined) {
            lastCode = result.code;
          }

          // Aguardar um momento antes de executar o próximo comando
          if (i < commandCount - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error) {
          Logger.error(`Erro ao executar linha ${i+1}: ${line}`, error);
          combinedError += `\n--- Erro ao executar: ${line} ---\n${error instanceof Error ? error.message : 'Erro desconhecido'}\n`;
          lastCode = 1;
        }
      }

      return {
        stdout: combinedOutput.trim(),
        stderr: combinedError.trim(),
        code: lastCode
      };
    };

    // Executar com timeout
    return Promise.race([executePromise(), timeoutPromise]);
  }

  /**
   * Executa um comando interativo
   * @param command Comando a ser executado
   * @param options Opções adicionais
   * @returns Resultado do comando
   */
  protected async executeInteractiveCommand(
    command: string,
    options: {
      term?: string;
      rows?: number;
      cols?: number;
      maxExecutionTime?: number;
      inactivityTimeout?: number;
      commandCount?: number; // Número de comandos para cálculo de timeout dinâmico
    } = {}
  ): Promise<CommandResult> {
    return new Promise((resolve, reject) => {
      let shell: any = null;
      let commandTimeout: NodeJS.Timeout | null = null;
      let globalTimeout: NodeJS.Timeout | null = null;

      // Proteção contra loops infinitos - limite absoluto de verificações
      let completionCheckCount = 0;
      const MAX_COMPLETION_CHECKS = calculateMaxCompletionChecks(options.commandCount || 1);

      // Valores padrão
      const term = options.term || 'vt100';
      const rows = options.rows || 24;
      const cols = options.cols || 80;

      // Calcular timeout dinâmico se o número de comandos for fornecido
      let MAX_EXECUTION_TIME: number;
      if (options.commandCount && options.commandCount > 1) {
        MAX_EXECUTION_TIME = this.calculateDynamicTimeout(options.commandCount);
        Logger.log(`Usando timeout dinâmico de ${MAX_EXECUTION_TIME}ms para ${options.commandCount} comandos`);
      } else {
        MAX_EXECUTION_TIME = options.maxExecutionTime || this.BASE_TIMEOUT;
      }

      const INACTIVITY_TIMEOUT = options.inactivityTimeout || LOOP_PROTECTION_CONFIG.TIMEOUTS.INACTIVITY_TIMEOUT_MS;

      try {
        let output = '';
        let errorOutput = '';
        let lastDataTime = Date.now();
        let commandCompleted = false;

        // Função para limpar recursos
        const cleanup = () => {
          if (commandTimeout) {
            clearTimeout(commandTimeout);
            commandTimeout = null;
          }

          if (globalTimeout) {
            clearTimeout(globalTimeout);
            globalTimeout = null;
          }

          if (shell) {
            // Remover todos os listeners para evitar vazamentos de memória
            shell.removeAllListeners('data');
            shell.removeAllListeners('error');
            shell.removeAllListeners('close');
            shell.removeAllListeners('end');

            if (!shell.ended) {
              try {
                shell.end();
              } catch (e) {
                Logger.error('Erro ao fechar shell:', e);
              }
            }
            shell = null;
          }
        };

        // Timeout global para garantir que o comando não execute indefinidamente
        globalTimeout = setTimeout(() => {
          Logger.warn(`Timeout global atingido após ${MAX_EXECUTION_TIME}ms para o comando: ${command}`);
          cleanup();
          resolve({
            stdout: output,
            stderr: errorOutput + `\n[ERRO] Timeout global atingido após ${MAX_EXECUTION_TIME/1000} segundos`,
            code: 124 // Código de timeout
          });
        }, MAX_EXECUTION_TIME);

        // Função para verificar se o comando foi concluído por inatividade
        const checkCompletion = () => {
          // Proteção contra loops infinitos - verificar limite de tentativas
          completionCheckCount++;
          if (completionCheckCount > MAX_COMPLETION_CHECKS) {
            Logger.warn(`Limite de verificações de conclusão atingido (${MAX_COMPLETION_CHECKS}), forçando finalização`);
            commandCompleted = true;
            cleanup();

            // Se há saída substancial (mais de 500 caracteres), não mostrar aviso
            const hasSubstantialOutput = output.length > 500;
            const warningMessage = hasSubstantialOutput ? '' : '\n[AVISO] Comando finalizado por limite de verificações de segurança.';

            resolve({
              stdout: output,
              stderr: errorOutput + warningMessage,
              code: 0
            });
            return;
          }

          const now = Date.now();
          const timeSinceLastData = now - lastDataTime;

          if (timeSinceLastData >= INACTIVITY_TIMEOUT && !commandCompleted) {
            Logger.log(`Comando concluído após ${timeSinceLastData}ms de inatividade - Check ${completionCheckCount}/${MAX_COMPLETION_CHECKS}`);
            commandCompleted = true;
            cleanup();
            resolve({
              stdout: output,
              stderr: errorOutput,
              code: 0
            });
          } else if (!commandCompleted && completionCheckCount < MAX_COMPLETION_CHECKS) {
            // Reagendar verificação apenas se não atingiu o limite
            commandTimeout = setTimeout(checkCompletion, 1000);
          } else if (completionCheckCount >= MAX_COMPLETION_CHECKS) {
            Logger.warn('Limite de verificações atingido durante espera, finalizando comando');
            commandCompleted = true;
            cleanup();

            // Se há saída substancial, não mostrar aviso
            const hasSubstantialOutput = output.length > 500;
            const warningMessage = hasSubstantialOutput ? '' : '\n[AVISO] Comando finalizado por limite de verificações de segurança.';

            resolve({
              stdout: output,
              stderr: errorOutput + warningMessage,
              code: 0
            });
          }
        };

        // Iniciar shell interativo
        this.ssh.requestShell({
          term,
          rows,
          cols,
          wrap: cols,
          ptyType: 'vanilla'
        }).then(shellInstance => {
          shell = shellInstance;

          shell.on('data', (data: Buffer) => {
            const chunk = data.toString('utf8');
            output += chunk;
            lastDataTime = Date.now();
            Logger.log(`stdout (${chunk.length} bytes): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);
          });

          shell.stderr?.on('data', (data: Buffer) => {
            const chunk = data.toString('utf8');
            errorOutput += chunk;
            lastDataTime = Date.now();
            Logger.error(`stderr (${chunk.length} bytes): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);
          });

          shell.on('error', (err: Error) => {
            Logger.error('Shell error:', err);
            errorOutput += `\n[ERRO] ${err.message}`;
            cleanup();
            reject(err);
          });

          shell.on('close', () => {
            Logger.log('Shell fechado');
            if (!commandCompleted) {
              commandCompleted = true;
              cleanup();
              resolve({
                stdout: output,
                stderr: errorOutput,
                code: 0
              });
            }
          });

          // Aguardar um momento para o shell inicializar
          setTimeout(() => {
            // Enviar o comando
            shell.write(command + '\n');

            // Iniciar verificação de conclusão
            commandTimeout = setTimeout(checkCompletion, INACTIVITY_TIMEOUT);
          }, 1000);
        }).catch(error => {
          Logger.error('Erro ao criar shell interativo:', error);
          cleanup();
          reject(error);
        });
      } catch (error) {
        Logger.error('Erro no shell interativo:', error);
        if (shell) {
          // Limpar recursos em caso de erro
          if (commandTimeout) {
            clearTimeout(commandTimeout);
            commandTimeout = null;
          }
          if (globalTimeout) {
            clearTimeout(globalTimeout);
            globalTimeout = null;
          }
          try {
            shell.removeAllListeners();
            if (!shell.ended) shell.end();
          } catch (e) {
            Logger.error('Erro ao limpar shell em caso de erro:', e);
          }
        }
        reject(error);
      }
    });
  }
}
