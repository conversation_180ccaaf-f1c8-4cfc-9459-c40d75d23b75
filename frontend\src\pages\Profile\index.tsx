import React, { useState } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { userService } from '../../services/userService'
import { UserForm } from '../../components/UserForm'
import { toast } from 'react-hot-toast'
import { Palette } from 'lucide-react'
import TerminalThemeModal from '../../components/TerminalThemeModal'
import { useAuth } from '../../contexts/AuthContext'

export function ProfilePage() {
  const [showThemeModal, setShowThemeModal] = useState(false)
  const { updateUserProfile } = useAuth()

  const { data: profile, isLoading } = useQuery({
    queryKey: ['profile'],
    queryFn: userService.getProfile
  })

  const updateMutation = useMutation({
    mutationFn: (data: { name?: string; password?: string }) => userService.updateProfile(data),
    onSuccess: (response, variables) => {
      toast.success('Perfil atualizado com sucesso!')

      // Atualizar o contexto do usuário com os novos dados
      if (variables.name) {
        updateUserProfile({ name: variables.name })
      }
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao atualizar perfil')
    }
  })

  const handleUpdateProfile = (data: { name?: string; password?: string }) => {
    updateMutation.mutate(data)
  }

  if (isLoading) {
    return <div>Carregando...</div>
  }

  if (!profile?.user) {
    return <div>Erro ao carregar perfil</div>
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100">Meu Perfil</h1>

        <div className="space-y-6">
          {/* Formulário de Perfil */}
          <div className="bg-white dark:bg-gray-800 shadow-md dark:shadow-gray-900/50 rounded-lg p-6">
            <UserForm
              onSubmit={handleUpdateProfile}
              initialData={profile?.user}
              isEditing={true}
              isAdmin={false}
              isOwnProfile={true}
            />
          </div>

          {/* Configurações do Terminal */}
          <div className="bg-white dark:bg-gray-800 shadow-md dark:shadow-gray-900/50 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">Configurações do Terminal</h2>
                <p className="text-sm text-gray-600 dark:text-gray-300">Personalize a aparência do seu terminal</p>
              </div>

              <button
                onClick={() => setShowThemeModal(true)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Palette className="h-4 w-4" />
                Configurar Temas
              </button>
            </div>

            {profile?.user?.terminalTheme && (
              <div className="mt-4">
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">Tema atual:</p>
                <div
                  className="inline-block px-3 py-2 rounded-lg text-sm font-medium"
                  style={{
                    backgroundColor: profile.user.terminalTheme.backgroundColor,
                    color: profile.user.terminalTheme.textColor
                  }}
                >
                  {profile.user.terminalTheme.name}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modal de Temas */}
      <TerminalThemeModal
        isOpen={showThemeModal}
        onClose={() => setShowThemeModal(false)}
      />
    </div>
  )
} 