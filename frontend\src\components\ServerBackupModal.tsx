import React, { Fragment, useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { 
  downloadServerBackup, 
  readBackupFile, 
  validateBackup, 
  importServer 
} from '../services/backupApi'
import { 
  ServerBackupData, 
  BackupValidationResult, 
  ImportServerDTO 
} from '../types/backup'
import { SSHServer } from '../types/server'
import { 
  Download, 
  Upload, 
  X, 
  AlertTriangle, 
  CheckCircle, 
  FileText,
  AlertCircle 
} from 'lucide-react'
import toast from 'react-hot-toast'

interface ServerBackupModalProps {
  isOpen: boolean
  onClose: () => void
  server?: SSHServer
  onServerUpdated: () => void
}

const importSchema = z.object({
  newServerName: z.string().optional(),
  overwriteExisting: z.boolean().default(false)
})

type ImportFormData = z.infer<typeof importSchema>

export default function ServerBackupModal({
  isOpen,
  onClose,
  server,
  onServerUpdated
}: ServerBackupModalProps) {
  const [activeTab, setActiveTab] = useState<'export' | 'import'>('export')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [backupData, setBackupData] = useState<ServerBackupData | null>(null)
  const [validationResult, setValidationResult] = useState<BackupValidationResult | null>(null)
  const [isValidating, setIsValidating] = useState(false)
  const [isImporting, setIsImporting] = useState(false)

  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm<ImportFormData>({
    resolver: zodResolver(importSchema),
    defaultValues: {
      overwriteExisting: false
    }
  })

  const overwriteExisting = watch('overwriteExisting')

  // Função para exportar host
  const handleExport = async () => {
    if (!server) return

    try {
      await downloadServerBackup(server.id, server.name)
      toast.success('Backup exportado com sucesso!')
    } catch (error) {
      console.error('Erro ao exportar backup:', error)
      toast.error('Erro ao exportar backup')
    }
  }

  // Função para selecionar arquivo
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.name.endsWith('.json')) {
      toast.error('Por favor, selecione um arquivo JSON válido')
      return
    }

    setSelectedFile(file)
    setBackupData(null)
    setValidationResult(null)

    try {
      const data = await readBackupFile(file)
      setBackupData(data)
      
      // Auto-preencher nome do host se não existir conflito
      if (data.server.name) {
        setValue('newServerName', data.server.name)
      }

      // Validar automaticamente
      await validateFile(data)
    } catch (error) {
      console.error('Erro ao ler arquivo:', error)
      toast.error('Erro ao ler arquivo de backup')
      setSelectedFile(null)
    }
  }

  // Função para validar arquivo
  const validateFile = async (data: ServerBackupData) => {
    setIsValidating(true)
    try {
      const result = await validateBackup(data)
      setValidationResult(result)
      
      if (result.serverExists) {
        setValue('overwriteExisting', false)
      }
    } catch (error) {
      console.error('Erro ao validar backup:', error)
      toast.error('Erro ao validar backup')
    } finally {
      setIsValidating(false)
    }
  }

  // Função para importar host
  const onSubmit = async (formData: ImportFormData) => {
    if (!backupData || !validationResult?.isValid) return

    setIsImporting(true)
    try {
      const importData: ImportServerDTO = {
        backupData,
        overwriteExisting: formData.overwriteExisting,
        newServerName: formData.newServerName
      }

      await importServer(importData)
      toast.success('Host importado com sucesso!')
      onServerUpdated()
      onClose()
      resetForm()
    } catch (error: any) {
      console.error('Erro ao importar host:', error)
      const errorMessage = error.response?.data?.error || 'Erro ao importar host'
      toast.error(errorMessage)
    } finally {
      setIsImporting(false)
    }
  }

  // Função para resetar formulário
  const resetForm = () => {
    setSelectedFile(null)
    setBackupData(null)
    setValidationResult(null)
    setValue('newServerName', '')
    setValue('overwriteExisting', false)
  }

  // Função para fechar modal
  const handleClose = () => {
    resetForm()
    onClose()
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                    Backup/Importação de Host
                  </Dialog.Title>
                  <button
                    onClick={handleClose}
                    className="text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100 transition-colors"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>

                {/* Tabs */}
                <div className="flex space-x-1 rounded-xl bg-blue-900/20 dark:bg-blue-800/30 p-1 mb-6">
                  <button
                    onClick={() => setActiveTab('export')}
                    className={`w-full rounded-lg py-2.5 text-sm font-medium leading-5 transition-all ${
                      activeTab === 'export'
                        ? 'bg-white dark:bg-gray-700 text-blue-700 dark:text-blue-300 shadow'
                        : 'text-blue-600 dark:text-blue-400 hover:bg-white/[0.12] dark:hover:bg-gray-700/50 hover:text-blue-700 dark:hover:text-blue-300'
                    }`}
                  >
                    <Download className="h-4 w-4 inline mr-2" />
                    Exportar
                  </button>
                  <button
                    onClick={() => setActiveTab('import')}
                    className={`w-full rounded-lg py-2.5 text-sm font-medium leading-5 transition-all ${
                      activeTab === 'import'
                        ? 'bg-white dark:bg-gray-700 text-blue-700 dark:text-blue-300 shadow'
                        : 'text-blue-600 dark:text-blue-400 hover:bg-white/[0.12] dark:hover:bg-gray-700/50 hover:text-blue-700 dark:hover:text-blue-300'
                    }`}
                  >
                    <Upload className="h-4 w-4 inline mr-2" />
                    Importar
                  </button>
                </div>

                {/* Export Tab */}
                {activeTab === 'export' && (
                  <div className="space-y-4">
                    {server ? (
                      <>
                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                          <h4 className="font-medium text-gray-900 dark:text-white mb-2">Host Selecionado:</h4>
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            <p><strong>Nome:</strong> {server.name}</p>
                            <p><strong>Host:</strong> {server.host}:{server.port}</p>
                            <p><strong>Tipo:</strong> {server.deviceType}</p>
                            <p><strong>Comandos:</strong> {server.commands?.length || 0}</p>
                          </div>
                        </div>

                        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                          <div className="flex items-start">
                            <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3" />
                            <div>
                              <h4 className="font-medium text-blue-900 dark:text-blue-300 mb-1">Sobre o Backup</h4>
                              <p className="text-sm text-blue-700 dark:text-blue-400">
                                O backup incluirá todas as configurações do host e seus comandos.
                                As credenciais (senha/chave privada) serão incluídas no arquivo JSON.
                              </p>
                            </div>
                          </div>
                        </div>

                        <button
                          onClick={handleExport}
                          className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Exportar Backup
                        </button>
                      </>
                    ) : (
                      <div className="text-center py-8">
                        <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                        <p className="text-gray-600 dark:text-gray-400">Nenhum host selecionado para exportação.</p>
                      </div>
                    )}
                  </div>
                )}

                {/* Import Tab */}
                {activeTab === 'import' && (
                  <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                    {/* File Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Selecionar Arquivo de Backup
                      </label>
                      <input
                        type="file"
                        accept=".json"
                        onChange={handleFileSelect}
                        className="block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 dark:file:bg-blue-900/20 file:text-blue-700 dark:file:text-blue-400 hover:file:bg-blue-100 dark:hover:file:bg-blue-900/30"
                      />
                    </div>

                    {/* Validation Results */}
                    {isValidating && (
                      <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 dark:border-yellow-400 mr-3"></div>
                          <span className="text-yellow-800 dark:text-yellow-400">Validando backup...</span>
                        </div>
                      </div>
                    )}

                    {validationResult && (
                      <div className={`rounded-lg p-4 ${
                        validationResult.isValid ? 'bg-green-50 dark:bg-green-900/20' : 'bg-red-50 dark:bg-red-900/20'
                      }`}>
                        <div className="flex items-start">
                          {validationResult.isValid ? (
                            <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5 mr-3" />
                          ) : (
                            <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 mr-3" />
                          )}
                          <div className="flex-1">
                            <h4 className={`font-medium mb-2 ${
                              validationResult.isValid ? 'text-green-900 dark:text-green-300' : 'text-red-900 dark:text-red-300'
                            }`}>
                              {validationResult.isValid ? 'Backup Válido' : 'Backup Inválido'}
                            </h4>

                            {validationResult.errors.length > 0 && (
                              <div className="mb-2">
                                <p className="text-sm font-medium text-red-800 dark:text-red-300 mb-1">Erros:</p>
                                <ul className="text-sm text-red-700 dark:text-red-400 list-disc list-inside">
                                  {validationResult.errors.map((error, index) => (
                                    <li key={index}>{error}</li>
                                  ))}
                                </ul>
                              </div>
                            )}

                            {validationResult.warnings.length > 0 && (
                              <div>
                                <p className="text-sm font-medium text-yellow-800 dark:text-yellow-300 mb-1">Avisos:</p>
                                <ul className="text-sm text-yellow-700 dark:text-yellow-400 list-disc list-inside">
                                  {validationResult.warnings.map((warning, index) => (
                                    <li key={index}>{warning}</li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Server Info */}
                    {backupData && (
                      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Informações do Backup:</h4>
                        <div className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                          <p><strong>Host:</strong> {backupData.server.name}</p>
                          <p><strong>Host:</strong> {backupData.server.host}:{backupData.server.port}</p>
                          <p><strong>Tipo:</strong> {backupData.server.deviceType}</p>
                          <p><strong>Comandos:</strong> {backupData.commands.length}</p>
                          <p><strong>Exportado em:</strong> {new Date(backupData.exportedAt).toLocaleString()}</p>
                          <p><strong>Exportado por:</strong> {backupData.exportedBy.name}</p>
                        </div>
                      </div>
                    )}

                    {/* Import Options */}
                    {validationResult?.isValid && (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Nome do Host (opcional)
                          </label>
                          <input
                            {...register('newServerName')}
                            type="text"
                            placeholder="Deixe vazio para usar o nome original"
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Se especificado, o host será importado com este nome
                          </p>
                        </div>

                        {validationResult.serverExists && (
                          <div className="flex items-center">
                            <input
                              {...register('overwriteExisting')}
                              type="checkbox"
                              id="overwriteExisting"
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                            />
                            <label htmlFor="overwriteExisting" className="ml-2 block text-sm text-gray-900 dark:text-white">
                              Sobrescrever host existente
                            </label>
                          </div>
                        )}

                        <button
                          type="submit"
                          disabled={isImporting}
                          className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isImporting ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Importando...
                            </>
                          ) : (
                            <>
                              <Upload className="h-4 w-4 mr-2" />
                              Importar Host
                            </>
                          )}
                        </button>
                      </div>
                    )}
                  </form>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
