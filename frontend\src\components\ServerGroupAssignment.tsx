import React, { useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { X, FolderPlus, Check, Plus } from 'lucide-react'
import { 
  listServerGroups, 
  addServerToGroup, 
  removeServerFromGroup 
} from '../services/serverGroupApi'
import { SSHServer } from '../types/server'
import { ServerGroup } from '../types/serverGroup'
import toast from 'react-hot-toast'

interface ServerGroupAssignmentProps {
  isOpen: boolean
  onClose: () => void
  server: SSHServer
  onServerUpdated: () => void
}

export default function ServerGroupAssignment({ 
  isOpen, 
  onClose, 
  server, 
  onServerUpdated 
}: ServerGroupAssignmentProps) {
  const queryClient = useQueryClient()

  // Buscar grupos
  const { data: groups, isLoading } = useQuery({
    queryKey: ['server-groups'],
    queryFn: listServerGroups,
    enabled: isOpen,
  })

  // Mutação para adicionar host ao grupo
  const addMutation = useMutation({
    mutationFn: ({ groupId }: { groupId: string }) => 
      addServerToGroup(groupId, { serverId: server.id }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['server-groups'] })
      queryClient.invalidateQueries({ queryKey: ['servers'] })
      onServerUpdated()
      toast.success('Host adicionado ao grupo!')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao adicionar host ao grupo')
    },
  })

  // Mutação para remover host do grupo
  const removeMutation = useMutation({
    mutationFn: ({ groupId }: { groupId: string }) => 
      removeServerFromGroup(groupId, server.id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['server-groups'] })
      queryClient.invalidateQueries({ queryKey: ['servers'] })
      onServerUpdated()
      toast.success('Host removido do grupo!')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao remover host do grupo')
    },
  })

  // Obter grupos aos quais o host pertence
  const serverGroupIds = server.groupMembers?.map(member => member.group.id) || []

  const handleToggleGroup = (group: ServerGroup) => {
    const isInGroup = serverGroupIds.includes(group.id)
    
    if (isInGroup) {
      removeMutation.mutate({ groupId: group.id })
    } else {
      addMutation.mutate({ groupId: group.id })
    }
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title as="div" className="flex justify-between items-center mb-4">
                  <div className="flex items-center gap-2">
                    <FolderPlus className="h-6 w-6 text-blue-600" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      Gerenciar Grupos
                    </h3>
                  </div>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </Dialog.Title>

                <div className="mb-4">
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Host: <span className="font-medium">{server.name}</span>
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Selecione os grupos aos quais este host deve pertencer
                  </p>
                </div>

                {isLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-gray-500 dark:text-gray-400">Carregando grupos...</p>
                  </div>
                ) : groups && groups.length > 0 ? (
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {groups.map((group) => {
                      const isInGroup = serverGroupIds.includes(group.id)
                      const isLoading = addMutation.isPending || removeMutation.isPending

                      return (
                        <button
                          key={group.id}
                          onClick={() => handleToggleGroup(group)}
                          disabled={isLoading}
                          className={`w-full flex items-center justify-between p-3 rounded-lg border transition-colors ${
                            isInGroup
                              ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-blue-900 dark:text-blue-300'
                              : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
                          } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          <div className="flex items-center gap-3">
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: group.color || '#3B82F6' }}
                            />
                            <div className="text-left">
                              <p className="font-medium">{group.name}</p>
                              {group.description && (
                                <p className="text-xs opacity-75">{group.description}</p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center">
                            {isInGroup ? (
                              <Check className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                            ) : (
                              <Plus className="h-5 w-5 text-gray-400" />
                            )}
                          </div>
                        </button>
                      )
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FolderPlus className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400">Nenhum grupo disponível.</p>
                    <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                      Crie grupos primeiro para organizar seus hosts.
                    </p>
                  </div>
                )}

                <div className="flex justify-end pt-4 mt-4 border-t border-gray-200 dark:border-gray-600">
                  <button
                    onClick={onClose}
                    className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  >
                    Fechar
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
