#!/bin/bash
# Script para configurar o backup diário no servidor Linux
# Este script deve ser executado no servidor de produção

# Verificar se está sendo executado como root
if [ "$(id -u)" -ne 0 ]; then
  echo "Este script deve ser executado como root"
  exit 1
fi

# Definir diretórios
PROJETO_DIR="/var/www/sem-fronteiras-ssh"
BACKUP_SCRIPT="$PROJETO_DIR/scripts/backup-externo.sh"
BACKUP_EXTERNO_DIR="/var/backups/sem-fronteiras"

# Criar diretório de backup externo se não existir
mkdir -p "$BACKUP_EXTERNO_DIR"
chmod 755 "$BACKUP_EXTERNO_DIR"

# Verificar se o script de backup existe
if [ ! -f "$BACKUP_SCRIPT" ]; then
  echo "Erro: Script de backup não encontrado em $BACKUP_SCRIPT"
  exit 1
fi

# Tornar o script executável
chmod +x "$BACKUP_SCRIPT"

# Configurar cron para executar o backup diariamente à meia-noite
CRON_ENTRY="0 0 * * * $BACKUP_SCRIPT > $BACKUP_EXTERNO_DIR/backup_log_\$(date +\%Y\%m\%d).log 2>&1"

# Verificar se a entrada já existe no crontab
CRON_CHECK=$(crontab -l 2>/dev/null | grep -F "$BACKUP_SCRIPT")

if [ -z "$CRON_CHECK" ]; then
  # Adicionar a entrada ao crontab
  (crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -
  echo "Backup diário configurado com sucesso. Será executado todos os dias à meia-noite."
else
  echo "Backup diário já está configurado."
fi

# Executar o script de backup uma vez para verificar
echo "Executando backup inicial para verificação..."
bash "$BACKUP_SCRIPT"

echo "Configuração de backup diário concluída."
