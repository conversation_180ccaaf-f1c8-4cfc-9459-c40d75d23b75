import React, { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { listCommandHistory } from '../services/api'
import { useAuth } from '../contexts/AuthContext'
import { Navigate, useParams, useNavigate, useLocation } from 'react-router-dom'
import { ChevronLeft, ChevronRight, Clock, Server, Terminal, User, X } from 'lucide-react'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { useTheme } from '../contexts/ThemeContext'

export function CommandHistory() {
  const { user } = useAuth()
  const { theme } = useTheme()
  const navigate = useNavigate()
  const location = useLocation()
  const { page } = useParams<{ page?: string }>()
  const [isResultModalOpen, setIsResultModalOpen] = useState(false)
  const [selectedResult, setSelectedResult] = useState<{ stdout?: string; stderr?: string; code?: number } | null>(null)
  const [selectedCommand, setSelectedCommand] = useState<{ name: string; command: string } | null>(null)
  const [currentPage, setCurrentPage] = useState(page ? parseInt(page) : 1)
  const [itemsPerPage] = useState(10)

  // Determinar o tema atual
  const actualTheme = theme === 'system'
    ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
    : theme

  // Atualiza o estado quando o parâmetro da URL muda
  useEffect(() => {
    if (page) {
      const pageNumber = parseInt(page)
      if (!isNaN(pageNumber) && pageNumber > 0) {
        setCurrentPage(pageNumber)
      } else {
        // Se o número da página for inválido, redireciona para a página 1
        navigate('/command-history/page/1', { replace: true })
      }
    }
  }, [page, navigate])

  const { data, isLoading, error } = useQuery({
    queryKey: ['commandHistory', currentPage, itemsPerPage],
    queryFn: () => listCommandHistory(currentPage, itemsPerPage),
  })

  // Efeito para redirecionar para a URL com número de página quando estamos na rota raiz
  useEffect(() => {
    if (location.pathname === '/command-history' && data?.pagination && !isLoading) {
      navigate(`/command-history/page/${currentPage}`, { replace: true })
    }
  }, [location.pathname, data?.pagination, currentPage, navigate, isLoading])

  const history = data?.data || []
  const pagination = data?.pagination

  // Função para abrir o modal de resultado
  const handleShowResult = (item: any) => {
    try {
      const result = JSON.parse(item.result)
      setSelectedResult(result)

      // Verificar se o comando existe ou usar os campos de backup
      if (item.command) {
        setSelectedCommand(item.command)
      } else if (item.commandName || item.commandText) {
        setSelectedCommand({
          name: item.commandName || 'Comando excluído',
          command: item.commandText || 'Comando não disponível'
        })
      } else {
        setSelectedCommand({
          name: 'Comando excluído',
          command: 'Informações do comando não disponíveis'
        })
      }

      setIsResultModalOpen(true)
    } catch (e) {
      alert(`Não foi possível exibir o resultado: ${item.result}`)
    }
  }

  // Função para fechar o modal de resultado
  const closeResultModal = () => {
    setIsResultModalOpen(false)
    setSelectedResult(null)
    setSelectedCommand(null)
  }

  // Função para atualizar a URL com a página atual
  const updateUrlWithPage = (pageNumber: number) => {
    // Se estamos na rota padrão sem número de página, navegamos para a rota com número
    if (location.pathname === '/command-history') {
      navigate(`/command-history/page/${pageNumber}`)
    } else {
      // Se já estamos na rota com número, apenas atualizamos o número
      navigate(`/command-history/page/${pageNumber}`)
    }
  }

  // Funções de paginação
  const handlePreviousPage = () => {
    if (currentPage > 1) {
      const newPage = currentPage - 1
      setCurrentPage(newPage)
      updateUrlWithPage(newPage)
    }
  }

  const handleNextPage = () => {
    if (pagination && currentPage < pagination.totalPages) {
      const newPage = currentPage + 1
      setCurrentPage(newPage)
      updateUrlWithPage(newPage)
    }
  }

  // Função para ir para uma página específica
  const goToPage = (pageNumber: number) => {
    setCurrentPage(pageNumber)
    updateUrlWithPage(pageNumber)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Erro!</strong>
        <span className="block sm:inline"> Não foi possível carregar o histórico de comandos.</span>
      </div>
    )
  }

  // Função para formatar a data
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    }).format(date)
  }

  // Função para formatar o status
  const formatStatus = (status: number) => {
    if (status === 0) return <span className="text-green-600 font-medium">Sucesso</span>
    return <span className="text-red-600 font-medium">Erro ({status})</span>
  }

  return (
    <div className="p-4 sm:p-6">
      <h1 className={`text-xl sm:text-2xl font-bold mb-4 sm:mb-6 ${actualTheme === 'dark' ? 'text-white' : 'text-gray-900'}`}>Histórico de Comandos</h1>

      {history.length === 0 ? (
        <div className={`text-center py-8 ${actualTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
          <Terminal className={`h-12 w-12 mx-auto mb-4 ${actualTheme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`} />
          <p>Nenhum comando foi executado ainda.</p>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto -mx-4 sm:mx-0">
            <div className="inline-block min-w-full align-middle">
              <div className={`overflow-hidden shadow-sm ring-1 ring-opacity-5 rounded-lg ${
                actualTheme === 'dark'
                  ? 'ring-gray-600'
                  : 'ring-black'
              }`}>
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className={actualTheme === 'dark' ? 'bg-gray-800' : 'bg-gray-50'}>
                    <tr>
                      <th scope="col" className={`px-3 sm:px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Data/Hora
                      </th>
                      <th scope="col" className={`px-3 sm:px-6 py-3 text-left text-xs font-medium uppercase tracking-wider hidden sm:table-cell ${
                        actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Usuário
                      </th>
                      <th scope="col" className={`px-3 sm:px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Servidor
                      </th>
                      <th scope="col" className={`px-3 sm:px-6 py-3 text-left text-xs font-medium uppercase tracking-wider hidden md:table-cell ${
                        actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Comando
                      </th>
                      <th scope="col" className={`px-3 sm:px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Status
                      </th>
                      <th scope="col" className={`px-3 sm:px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                        actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        Ações
                      </th>
                    </tr>
                  </thead>
                  <tbody className={`divide-y ${
                    actualTheme === 'dark'
                      ? 'bg-gray-900 divide-gray-700'
                      : 'bg-white divide-gray-200'
                  }`}>
                    {history.map((item) => (
                      <tr key={item.id} className={actualTheme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-gray-50'}>
                        <td className={`px-3 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm ${
                          actualTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                          {formatDate(item.executedAt)}
                        </td>
                        <td className="px-3 sm:px-6 py-4 whitespace-nowrap hidden sm:table-cell">
                          <div className="flex flex-col">
                            <span className={`text-xs sm:text-sm font-medium ${
                              actualTheme === 'dark' ? 'text-gray-200' : 'text-gray-900'
                            }`}>{item.user.name}</span>
                            {item.user.active === false && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                Inativo
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                          <div className={`text-xs sm:text-sm ${
                            actualTheme === 'dark' ? 'text-gray-200' : 'text-gray-900'
                          }`}>{item.server.name}</div>
                          <div className={`text-xs truncate max-w-[100px] sm:max-w-none ${
                            actualTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                          }`}>{item.server.host}</div>
                        </td>
                        <td className="px-3 sm:px-6 py-4 whitespace-nowrap hidden md:table-cell">
                          <div className={`text-xs sm:text-sm truncate max-w-[150px] lg:max-w-xs ${
                            actualTheme === 'dark' ? 'text-gray-200' : 'text-gray-900'
                          }`}
                               title={item.command?.command || item.commandText || 'Comando não disponível'}>
                            {item.command?.name || item.commandName || 'Comando excluído'}
                          </div>
                        </td>
                        <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                          {formatStatus(item.status)}
                        </td>
                        <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-right text-xs sm:text-sm font-medium">
                          <button
                            onClick={() => handleShowResult(item)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Ver
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Paginação */}
          {pagination && (
            <div className={`mt-6 flex items-center justify-between border-t px-4 py-3 sm:px-6 ${
              actualTheme === 'dark'
                ? 'border-gray-700 bg-gray-800'
                : 'border-gray-200 bg-white'
            }`}>
              {/* Informação de itens */}
              <div className={`hidden sm:block text-sm ${
                actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
              }`}>
                Mostrando <span className="font-medium">{((currentPage - 1) * itemsPerPage) + 1}</span> até{' '}
                <span className="font-medium">
                  {Math.min(currentPage * itemsPerPage, pagination.total)}
                </span>{' '}
                de <span className="font-medium">{pagination.total}</span> resultados
              </div>

              {/* Versão mobile */}
              <div className={`sm:hidden text-sm ${
                actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
              }`}>
                <span className="font-medium">{currentPage}</span> de{' '}
                <span className="font-medium">{pagination.totalPages}</span> páginas
              </div>

              {/* Controles de paginação */}
              <div className="flex items-center gap-1">
                <button
                  onClick={handlePreviousPage}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center rounded-l-md px-2 py-2 text-sm font-medium ${
                    currentPage === 1
                      ? (actualTheme === 'dark'
                          ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                          : 'bg-gray-100 text-gray-400 cursor-not-allowed')
                      : (actualTheme === 'dark'
                          ? 'bg-gray-700 text-gray-300 ring-1 ring-inset ring-gray-600 hover:bg-gray-600'
                          : 'bg-white text-gray-500 ring-1 ring-inset ring-gray-300 hover:bg-gray-50')
                  }`}
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>

                {/* Números das páginas */}
                <div className="flex">
                  {Array.from({ length: Math.min(3, pagination.totalPages) }).map((_, idx) => {
                    let pageNumber;
                    if (pagination.totalPages <= 3) {
                      pageNumber = idx + 1;
                    } else if (currentPage <= 2) {
                      pageNumber = idx + 1;
                    } else if (currentPage >= pagination.totalPages - 1) {
                      pageNumber = pagination.totalPages - 2 + idx;
                    } else {
                      pageNumber = currentPage - 1 + idx;
                    }

                    if (pageNumber <= pagination.totalPages) {
                      return (
                        <button
                          key={pageNumber}
                          onClick={() => goToPage(pageNumber)}
                          className={`relative inline-flex items-center px-4 py-2 text-sm font-medium ${
                            currentPage === pageNumber
                              ? 'z-10 bg-blue-600 text-white focus:z-20'
                              : (actualTheme === 'dark'
                                  ? 'text-gray-300 ring-1 ring-inset ring-gray-600 hover:bg-gray-700'
                                  : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50')
                          } ${idx === 0 ? '-ml-px' : ''}`}
                        >
                          {pageNumber}
                        </button>
                      );
                    }
                    return null;
                  })}
                </div>

                <button
                  onClick={handleNextPage}
                  disabled={currentPage === pagination.totalPages}
                  className={`relative inline-flex items-center rounded-r-md px-2 py-2 text-sm font-medium ${
                    currentPage === pagination.totalPages
                      ? (actualTheme === 'dark'
                          ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                          : 'bg-gray-100 text-gray-400 cursor-not-allowed')
                      : (actualTheme === 'dark'
                          ? 'bg-gray-700 text-gray-300 ring-1 ring-inset ring-gray-600 hover:bg-gray-600'
                          : 'bg-white text-gray-500 ring-1 ring-inset ring-gray-300 hover:bg-gray-50')
                  }`}
                >
                  <ChevronRight className="h-5 w-5" />
                </button>
              </div>
            </div>
          )}
        </>
      )}

      {/* Modal de resultado */}
      <Transition appear show={isResultModalOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeResultModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className={`w-full max-w-3xl transform overflow-hidden rounded-2xl p-6 text-left align-middle shadow-xl transition-all ${
                  actualTheme === 'dark' ? 'bg-gray-800' : 'bg-white'
                }`}>
                  <div className="flex justify-between items-start">
                    <Dialog.Title
                      as="h3"
                      className={`text-lg font-medium leading-6 ${
                        actualTheme === 'dark' ? 'text-white' : 'text-gray-900'
                      }`}
                    >
                      Resultado do Comando
                    </Dialog.Title>
                    <button
                      type="button"
                      className={actualTheme === 'dark' ? 'text-gray-400 hover:text-gray-300' : 'text-gray-400 hover:text-gray-500'}
                      onClick={closeResultModal}
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>

                  <div className="mt-4">
                    <div className={`p-3 rounded-md mb-4 ${
                      actualTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'
                    }`}>
                      <p className={`text-sm font-medium ${
                        actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                      }`}>Comando executado:</p>
                      <pre className={`mt-1 text-sm p-2 rounded overflow-x-auto ${
                        actualTheme === 'dark'
                          ? 'text-gray-200 bg-gray-800'
                          : 'text-gray-800 bg-gray-100'
                      }`}>
                        {selectedCommand?.command}
                      </pre>
                    </div>

                    {selectedResult && (
                      <div className="space-y-4">
                        {selectedResult.stdout && (
                          <div>
                            <p className={`text-sm font-medium ${
                              actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                            }`}>Saída padrão:</p>
                            <pre className={`mt-1 text-sm p-2 rounded overflow-x-auto max-h-60 overflow-y-auto ${
                              actualTheme === 'dark'
                                ? 'text-gray-200 bg-gray-800'
                                : 'text-gray-800 bg-gray-100'
                            }`}>
                              {selectedResult.stdout}
                            </pre>
                          </div>
                        )}

                        {selectedResult.stderr && (
                          <div>
                            <p className={`text-sm font-medium ${
                              actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                            }`}>Saída de erro:</p>
                            <pre className={`mt-1 text-sm p-2 rounded overflow-x-auto max-h-60 overflow-y-auto ${
                              actualTheme === 'dark'
                                ? 'text-gray-200 bg-gray-800'
                                : 'text-gray-800 bg-gray-100'
                            }`}>
                              {selectedResult.stderr}
                            </pre>
                          </div>
                        )}

                        <div>
                          <p className={`text-sm font-medium ${
                            actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                          }`}>Código de saída:</p>
                          <p className={`mt-1 text-sm ${selectedResult.code === 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {selectedResult.code === 0 ? 'Sucesso (0)' : `Erro (${selectedResult.code})`}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="mt-6">
                    <button
                      type="button"
                      className={`inline-flex justify-center rounded-md border border-transparent px-4 py-2 text-sm font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 ${
                        actualTheme === 'dark'
                          ? 'bg-blue-700 text-blue-100 hover:bg-blue-600'
                          : 'bg-blue-100 text-blue-900 hover:bg-blue-200'
                      }`}
                      onClick={closeResultModal}
                    >
                      Fechar
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  )
}