# Sistema de Temas Dark/Light

Este documento descreve como usar e estender o sistema de temas dark/light implementado no projeto.

## Visão Geral

O sistema de temas permite alternar entre três modos:
- **Light**: Tema claro padrão
- **Dark**: Tema escuro
- **System**: Segue a preferência do sistema operacional

## Componentes Principais

### 1. ThemeContext (`src/contexts/ThemeContext.tsx`)

Gerencia o estado global do tema e fornece funções para alternar entre temas.

```tsx
import { useTheme } from '../contexts/ThemeContext'

function MyComponent() {
  const { theme, actualTheme, setTheme, toggleTheme } = useTheme()
  
  return (
    <div>
      <p>Tema atual: {actualTheme}</p>
      <button onClick={toggleTheme}>Alternar Tema</button>
    </div>
  )
}
```

### 2. ThemeToggle (`src/components/ThemeToggle.tsx`)

Componente dropdown para seleção de tema com ícones visuais.

```tsx
import { ThemeToggle } from '../components/ThemeToggle'

function Header() {
  return (
    <header>
      <ThemeToggle />
    </header>
  )
}
```

## Configuração do Tailwind

O arquivo `tailwind.config.js` foi configurado com:

```js
export default {
  darkMode: 'class', // Habilita dark mode baseado em classe
  theme: {
    extend: {
      colors: {
        primary: { /* cores azuis */ },
        secondary: { /* cores laranja */ },
        danger: { /* cores vermelhas */ }
      }
    }
  }
}
```

## Classes CSS Recomendadas

### Backgrounds
```css
bg-white dark:bg-gray-800
bg-gray-50 dark:bg-gray-900
bg-gray-100 dark:bg-gray-800
```

### Textos
```css
text-gray-900 dark:text-gray-100
text-gray-700 dark:text-gray-200
text-gray-600 dark:text-gray-300
text-gray-500 dark:text-gray-400
```

### Bordas
```css
border-gray-200 dark:border-gray-700
border-gray-300 dark:border-gray-600
```

### Estados Hover
```css
hover:bg-gray-100 dark:hover:bg-gray-700
hover:text-gray-900 dark:hover:text-white
```

### Estados Focus
```css
focus:ring-blue-500 dark:focus:ring-blue-400
focus:ring-offset-2 dark:focus:ring-offset-gray-800
```

### Sombras
```css
shadow-md dark:shadow-gray-900/50
```

## Componentes Comuns

### Card
```tsx
import { Card, CardHeader, CardTitle, CardContent } from '../components/common'

<Card>
  <CardHeader>
    <CardTitle>Título</CardTitle>
  </CardHeader>
  <CardContent>
    Conteúdo que se adapta ao tema
  </CardContent>
</Card>
```

### Button
```tsx
import { Button } from '../components/common'
import { Plus } from 'lucide-react'

<Button variant="primary" icon={Plus}>
  Adicionar
</Button>
```

## Persistência

O tema selecionado é salvo no `localStorage` com a chave `sem-fronteiras:theme` e aplicado automaticamente na próxima visita.

## Transições

Todas as mudanças de tema incluem a classe `transition-colors` para suavizar as transições visuais.

## Scrollbars Customizadas

O CSS global inclui estilos para scrollbars que se adaptam ao tema:
- `.custom-scrollbar`: Scrollbar padrão
- `.custom-scrollbar-dark`: Scrollbar para áreas escuras
- `.custom-scrollbar-commands`: Scrollbar especial para listas de comandos

## Boas Práticas

1. **Sempre use classes dark:** junto com as classes normais
2. **Inclua transition-colors** para suavizar mudanças
3. **Teste em ambos os temas** durante o desenvolvimento
4. **Use os componentes comuns** quando possível
5. **Mantenha contraste adequado** em ambos os temas

## Exemplo Completo

```tsx
import React from 'react'
import { useTheme } from '../contexts/ThemeContext'
import { Card, CardContent, Button } from '../components/common'
import { Sun, Moon } from 'lucide-react'

function ExampleComponent() {
  const { actualTheme, toggleTheme } = useTheme()
  
  return (
    <Card className="max-w-md mx-auto">
      <CardContent>
        <div className="flex items-center justify-between">
          <span className="text-gray-700 dark:text-gray-200">
            Tema atual: {actualTheme}
          </span>
          <Button 
            variant="outline" 
            icon={actualTheme === 'light' ? Moon : Sun}
            onClick={toggleTheme}
          >
            Alternar
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
```

## Troubleshooting

### Tema não está mudando
- Verifique se o `ThemeProvider` está envolvendo a aplicação
- Confirme se as classes `dark:` estão sendo aplicadas corretamente

### Cores não estão corretas
- Verifique se a classe `dark` está sendo aplicada ao elemento `html`
- Confirme a configuração `darkMode: 'class'` no Tailwind

### Transições não funcionam
- Adicione `transition-colors` às classes do elemento
- Verifique se não há conflitos de CSS
