const { DatacomExecutor } = require('./dist/services/ssh/executors/datacomExecutor');
const { Logger } = require('./dist/utils/logger');

async function testDatacomTelnet() {
  console.log('🔌 Testando conexão Telnet para dispositivo Datacom...');
  
  try {
    // Criar executor Datacom com parâmetros de teste
    const executor = new DatacomExecutor(
      '*************', // Host de exemplo
      23,               // Porta Telnet padrão
      'admin',          // Usuário de exemplo
      'password'        // Senha de exemplo
    );
    
    console.log('✅ Executor Datacom criado com sucesso!');
    console.log('📋 Configurações:');
    console.log('   - Host: *************');
    console.log('   - Porta: 23 (Telnet)');
    console.log('   - Protocolo: Telnet');
    
    // Testar método executeCommand (sem conectar realmente)
    console.log('🧪 Testando interface do executor...');
    
    if (typeof executor.executeCommand === 'function') {
      console.log('✅ Método executeCommand disponível');
    } else {
      console.log('❌ Método executeCommand não encontrado');
    }
    
    if (typeof executor.cleanup === 'function') {
      console.log('✅ Método cleanup disponível');
    } else {
      console.log('❌ Método cleanup não encontrado');
    }
    
    console.log('🎉 Teste de interface concluído com sucesso!');
    console.log('📝 O DatacomExecutor agora usa Telnet ao invés de SSH');
    
  } catch (error) {
    console.error('❌ Erro durante o teste:', error.message);
  }
}

// Executar teste se este arquivo for executado diretamente
if (require.main === module) {
  testDatacomTelnet();
}

module.exports = { testDatacomTelnet };
