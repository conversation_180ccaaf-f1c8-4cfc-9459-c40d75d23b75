import { FastifyInstance } from 'fastify'
import { z } from 'zod'
import { PrismaClient, Role } from '@prisma/client'
import { createServer, updateServer, executeCommand, listCommandHistory, updateCommandsOrder, searchCommands } from '../controllers/servers'
import { verifyJWT } from '../middlewares/auth'
import { verifyAdmin } from '../middlewares/admin'

const prisma = new PrismaClient()

export async function serverRoutes(app: FastifyInstance) {
  // Middleware de autenticação para todas as rotas
  app.addHook('onRequest', verifyJWT)

  // Listar servidores do usuário
  app.get('/', async (request) => {
    // Buscar servidores onde o usuário é o proprietário
    const ownedServers = await prisma.server.findMany({
      where: {
        userId: request.user.id,
      },
      include: {
        commands: true,
        groupMembers: {
          include: {
            group: {
              select: {
                id: true,
                name: true,
                color: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc'
      },
    })

    // Buscar IDs dos servidores aos quais o usuário tem acesso
    const serverAccess = await prisma.$queryRaw`
      SELECT "serverId" FROM "ServerUser" WHERE "userId" = ${request.user.id}
    `

    // Converter o resultado para um array de IDs
    const accessServerIds = Array.isArray(serverAccess)
      ? serverAccess.map((row: any) => row.serverId)
      : []

    // Buscar os servidores aos quais o usuário tem acesso
    const accessibleServers = accessServerIds.length > 0
      ? await prisma.server.findMany({
          where: {
            id: {
              in: accessServerIds,
            },
          },
          include: {
            commands: true,
            groupMembers: {
              include: {
                group: {
                  select: {
                    id: true,
                    name: true,
                    color: true,
                  },
                },
              },
            },
          },
          orderBy: {
            createdAt: 'desc'
          },
        })
      : []

    // Combinar os resultados
    const allServers = [...ownedServers, ...accessibleServers]

    // Remover duplicatas (caso um usuário seja proprietário e também tenha acesso explícito)
    const uniqueServers = allServers.filter(
      (server, index, self) =>
        index === self.findIndex(s => s.id === server.id)
    )

    return { servers: uniqueServers }
  })

  // Obter servidor por ID
  app.get('/:id', async (request, reply) => {
    const paramsSchema = z.object({
      id: z.string().uuid(),
    })

    const { id } = paramsSchema.parse(request.params)

    // Verificar se o servidor existe
    const server = await prisma.server.findUnique({
      where: { id },
      include: {
        commands: true,
      },
    })

    if (!server) {
      return reply.status(404).send({ error: 'Servidor não encontrado' })
    }

    // Verificar se o usuário tem acesso ao servidor
    const isOwner = server.userId === request.user.id
    const isAdmin = request.user.role === Role.ADMIN

    if (!isOwner && !isAdmin) {
      // Verificar se o usuário tem acesso explícito ao servidor
      const hasAccess = await prisma.serverUser.findFirst({
        where: {
          serverId: id,
          userId: request.user.id,
        },
      })

      if (!hasAccess) {
        return reply.status(403).send({ error: 'Acesso negado' })
      }
    }

    return reply.send(server)
  })

  // Criar servidor (apenas admin)
  app.post('/', { onRequest: [verifyJWT, verifyAdmin] }, createServer)

  // Atualizar servidor (apenas admin)
  app.put('/:id', { onRequest: [verifyJWT, verifyAdmin] }, updateServer)

  // Deletar servidor
  app.delete('/:id', async (request, reply) => {
    const paramsSchema = z.object({
      id: z.string().uuid(),
    })

    const { id } = paramsSchema.parse(request.params)

    // Verificar se o servidor existe
    const server = await prisma.server.findUnique({
      where: { id },
    })

    if (!server) {
      return reply.status(404).send({ message: 'Servidor não encontrado' })
    }

    // Verificar se o usuário é o proprietário do servidor ou um administrador
    if (server.userId !== request.user.id && request.user.role !== Role.ADMIN) {
      return reply.status(403).send({ message: 'Acesso negado. Apenas o proprietário ou administradores podem excluir o servidor.' })
    }

    await prisma.server.delete({
      where: { id },
    })

    return { message: 'Servidor deletado com sucesso' }
  })

  // Executar comando
  app.post('/:id/execute', executeCommand)

  // Atualizar ordem dos comandos
  app.put('/:id/commands-order', updateCommandsOrder)

  // Listar histórico de comandos (acessível para todos os usuários)
  app.get('/command-history', listCommandHistory)

  // Buscar comandos por nome
  app.get('/search-commands', searchCommands)
}