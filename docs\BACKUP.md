# Sistema de Backup

Este documento descreve o sistema de backup implementado para a aplicação Sem Fronteiras SSH.

## Visão Geral

O sistema possui **dois mecanismos de backup independentes** para garantir redundância e confiabilidade:

1. **Backup via Container Docker** (pg_backup)
2. **Backup via Script Cron** (backup-externo.sh)

## 1. Backup via Container Docker

### Configuração
- **Container**: `pg_backup` (Alpine Linux + PostgreSQL Client)
- **Frequência**: A cada 24 horas
- **Localização**: `/var/www/sem-fronteiras-ssh/backups-externos/`
- **Retenção**: 30 dias (limpeza automática)

### Como Funciona
O container `pg_backup` é definido no `docker-compose.yml` e executa automaticamente:
- Conecta no banco PostgreSQL
- Gera backup usando `pg_dump`
- Salva no diretório mapeado
- Remove backups antigos (>30 dias)

### Verificar Status
```bash
docker ps | grep pg_backup
docker logs pg_backup --tail 10
```

## 2. Backup via Script Cron

### Configuração
- **Script**: `scripts/backup-externo.sh`
- **Frequência**: Diariamente à meia-noite (00:00)
- **Localização**: `/var/backups/sem-fronteiras/`
- **Retenção**: 30 dias (limpeza automática)

### Como Funciona
O script é executado pelo cron e:
- Conecta no container PostgreSQL
- Gera backup usando `pg_dump`
- Copia para diretório externo
- Gera logs de execução
- Remove backups antigos

### Verificar Configuração
```bash
crontab -l | grep backup
ls -la /var/backups/sem-fronteiras/
```

## 3. Inicialização Automática

### Serviço Systemd
- **Arquivo**: `/etc/systemd/system/sem-fronteiras.service`
- **Função**: Inicia automaticamente todos os containers após reinicialização
- **Dependência**: Aguarda o Docker estar ativo

### Comandos Úteis
```bash
# Verificar status do serviço
systemctl status sem-fronteiras.service

# Habilitar inicialização automática
systemctl enable sem-fronteiras.service

# Iniciar/parar manualmente
systemctl start sem-fronteiras.service
systemctl stop sem-fronteiras.service
```

## 4. Monitoramento

### Script de Verificação
Use o script `verificar-backups.sh` para verificar o status completo:

```bash
cd /var/www/sem-fronteiras-ssh/scripts
./verificar-backups.sh
```

### O que é Verificado
- ✅ Status do container pg_backup
- ✅ Backups recentes (container)
- ✅ Backups recentes (cron)
- ✅ Configuração do cron
- ✅ Espaço em disco
- ✅ Status do serviço systemd

## 5. Restauração de Backup

### Restaurar do Backup Mais Recente
```bash
# Encontrar o backup mais recente
LATEST_BACKUP=$(ls -t /var/backups/sem-fronteiras/backup_*.sql | head -1)

# Restaurar (CUIDADO: apaga dados existentes)
docker exec -i sem-fronteiras-ssh-postgres-1 psql -U postgres -d sem_fronteiras < "$LATEST_BACKUP"
```

### Restaurar de Backup Específico
```bash
# Listar backups disponíveis
ls -la /var/backups/sem-fronteiras/backup_*.sql

# Restaurar backup específico
docker exec -i sem-fronteiras-ssh-postgres-1 psql -U postgres -d sem_fronteiras < /var/backups/sem-fronteiras/backup_YYYYMMDD_HHMMSS.sql
```

## 6. Localização dos Arquivos

### Backups
- **Container**: `/var/www/sem-fronteiras-ssh/backups-externos/`
- **Script Cron**: `/var/backups/sem-fronteiras/`

### Logs
- **Cron**: `/var/backups/sem-fronteiras/backup_log_YYYYMMDD.log`
- **Container**: `docker logs pg_backup`

### Scripts
- **Backup Externo**: `/var/www/sem-fronteiras-ssh/scripts/backup-externo.sh`
- **Configuração**: `/var/www/sem-fronteiras-ssh/scripts/configurar-backup-diario.sh`
- **Verificação**: `/var/www/sem-fronteiras-ssh/scripts/verificar-backups.sh`

## 7. Solução de Problemas

### Container pg_backup não está rodando
```bash
cd /var/www/sem-fronteiras-ssh
docker-compose up -d pg_backup
```

### Cron não está executando
```bash
# Verificar se está configurado
crontab -l | grep backup

# Reconfigurar se necessário
./scripts/configurar-backup-diario.sh
```

### Serviço não inicia automaticamente
```bash
# Verificar status
systemctl status sem-fronteiras.service

# Habilitar se necessário
systemctl enable sem-fronteiras.service
```

### Espaço em disco insuficiente
```bash
# Verificar espaço
df -h /

# Limpar backups antigos manualmente
find /var/backups/sem-fronteiras -name "backup_*.sql" -mtime +30 -delete
find /var/www/sem-fronteiras-ssh/backups-externos -name "backup_*.sql" -mtime +30 -delete
```

## 8. Configuração Inicial

### Setup Automático (Recomendado)

Para configurar automaticamente todo o sistema em um novo servidor:

```bash
cd /var/www/sem-fronteiras-ssh
sudo ./scripts/setup-servidor.sh
```

Este script fará automaticamente:
- ✅ Configuração do backup diário via cron
- ✅ Instalação do serviço systemd
- ✅ Criação dos diretórios necessários
- ✅ Configuração de permissões
- ✅ Inicialização dos containers
- ✅ Verificação completa do sistema

### Setup Manual (Alternativo)

Para configurar manualmente o sistema de backup:

```bash
# 1. Configurar backup diário
cd /var/www/sem-fronteiras-ssh
sudo ./scripts/configurar-backup-diario.sh

# 2. Instalar serviço systemd
sudo cp scripts/sem-fronteiras.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable sem-fronteiras.service

# 3. Iniciar containers
sudo systemctl start sem-fronteiras.service

# 4. Verificar status
./scripts/verificar-backups.sh
```
