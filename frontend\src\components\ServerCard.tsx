import { useState, useRef, useEffect } from 'react'
import { Pencil, Trash2, Terminal, HardDrive, Users, FolderPlus, Download, MoreVertical } from 'lucide-react'
import { SSHServer, Command } from '../types/server'
import CommandModal from './CommandModal'
import EditServerModal from './EditServerModal'
import ConfirmModal from './ConfirmModal'
import ServerUserAccess from './ServerUserAccess'
import ServerGroupAssignment from './ServerGroupAssignment'
import ServerBackupModal from './ServerBackupModal'
import { api } from '../lib/api'
import { useAuth } from '../contexts/AuthContext'
import { usePermissions } from '../hooks/usePermissions'

interface ServerCardProps {
  server: SSHServer & { commands: Command[] }
  onServerUpdated: () => void
}

export function ServerCard({ server, onServerUpdated }: ServerCardProps) {
  const { user: currentUser } = useAuth()
  const { isAdmin, canManageServers, canBackupServers, canManageGroups } = usePermissions()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false)
  const [isUserAccessModalOpen, setIsUserAccessModalOpen] = useState(false)
  const [isGroupAssignmentOpen, setIsGroupAssignmentOpen] = useState(false)
  const [isBackupModalOpen, setIsBackupModalOpen] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Verificar se o usuário atual é o proprietário do servidor ou um administrador
  const isOwnerOrAdmin = server.userId === currentUser?.id || isAdmin

  // Verificar se há ações disponíveis no dropdown para o usuário atual
  const hasDropdownActions = canBackupServers || canManageGroups || isAdmin || isOwnerOrAdmin

  // Fechar dropdown quando clicar fora e calcular posição
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Toggle do dropdown
  const handleDropdownToggle = () => {
    setIsDropdownOpen(!isDropdownOpen)
  }

  async function handleDelete() {
    try {
      await api.delete(`/api/servers/${server.id}`)
      onServerUpdated()
    } catch (error) {
      console.error('Erro ao excluir servidor:', error)
      alert('Erro ao excluir servidor. Por favor, tente novamente.')
    }
  }

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg dark:shadow-gray-900/50 border border-gray-100 dark:border-gray-700 hover:border-primary-100 dark:hover:border-primary-700 transition-all duration-300 relative h-full flex flex-col">
        <div className="p-4 sm:p-6 space-y-3 sm:space-y-4 flex-1 flex flex-col">
          {/* Cabeçalho com nome e ações */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2 sm:gap-3">
              <HardDrive className="h-5 w-5 sm:h-6 sm:w-6 text-primary-500 dark:text-primary-400" />
              <h2 className="text-sm sm:text-md font-semibold text-gray-800 dark:text-gray-200 tracking-tight">{server.name}</h2>
            </div>

            {/* Menu de ações - apenas mostrar se há ações disponíveis */}
            {hasDropdownActions && (
              <div className="relative" ref={dropdownRef}>
                <button
                  onClick={handleDropdownToggle}
                  className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  title="Ações do servidor"
                >
                  <MoreVertical className="h-4 w-4" />
                </button>

              {/* Menu dropdown */}
              {isDropdownOpen && (
                <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-2xl dark:shadow-gray-900/50 border border-gray-200 dark:border-gray-700 py-1"
                style={{ zIndex: 9999 }}>
                  {/* Backup/Importação - apenas para administradores */}
                  {canBackupServers && (
                    <button
                      onClick={() => {
                        setIsBackupModalOpen(true)
                        setIsDropdownOpen(false)
                      }}
                      className="w-full px-3 py-2 text-left text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 transition-colors"
                    >
                      <Download className="h-4 w-4 text-green-600 dark:text-green-400" />
                      Backup/Importação
                    </button>
                  )}

                  {/* Gerenciar grupos - apenas para administradores */}
                  {canManageGroups && (
                    <button
                      onClick={() => {
                        setIsGroupAssignmentOpen(true)
                        setIsDropdownOpen(false)
                      }}
                      className="w-full px-3 py-2 text-left text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 transition-colors"
                    >
                      <FolderPlus className="h-4 w-4 text-primary-600 dark:text-primary-400" />
                      Gerenciar Grupos
                    </button>
                  )}

                  {/* Gerenciar usuários - apenas para admin */}
                  {currentUser?.role === 'ADMIN' && (
                    <button
                      onClick={() => {
                        setIsUserAccessModalOpen(true)
                        setIsDropdownOpen(false)
                      }}
                      className="w-full px-3 py-2 text-left text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 transition-colors"
                    >
                      <Users className="h-4 w-4 text-primary-600 dark:text-primary-400" />
                      Gerenciar Usuários
                    </button>
                  )}

                  {/* Separador */}
                  {isOwnerOrAdmin && (
                    <div className="border-t border-gray-100 dark:border-gray-700 my-1"></div>
                  )}

                  {/* Ações do proprietário/admin */}
                  {isOwnerOrAdmin && (
                    <>
                      <button
                        onClick={() => {
                          setIsEditModalOpen(true)
                          setIsDropdownOpen(false)
                        }}
                        className="w-full px-3 py-2 text-left text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 transition-colors"
                      >
                        <Pencil className="h-4 w-4 text-primary-600 dark:text-primary-400" />
                        Editar Host
                      </button>
                      <button
                        onClick={() => {
                          setIsConfirmDeleteOpen(true)
                          setIsDropdownOpen(false)
                        }}
                        className="w-full px-3 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center gap-2 transition-colors"
                      >
                        <Trash2 className="h-4 w-4" />
                        Excluir Host
                      </button>
                    </>
                  )}
                </div>
              )}
              </div>
            )}
          </div>

          {/* Grupos do servidor */}
          <div className="space-y-2 sm:space-y-3 pt-1 sm:pt-2 flex-1">
            <div className="min-h-[28px] flex items-start">
              {server.groupMembers && server.groupMembers.length > 0 ? (
                <div className="flex flex-wrap gap-1 sm:gap-2 w-full">
                  {server.groupMembers.map((member) => (
                    <div
                      key={member.id}
                      className="flex items-center gap-1 px-2 py-1 rounded-full text-xs"
                      style={{
                        backgroundColor: `${member.group.color || '#3B82F6'}20`,
                        color: member.group.color || '#3B82F6'
                      }}
                    >
                      <div
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: member.group.color || '#3B82F6' }}
                      />
                      <span className="font-medium">{member.group.name}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-xs text-gray-400 dark:text-gray-500 italic">
                  Nenhum grupo atribuído
                </div>
              )}
            </div>
          </div>

          {/* Botão de terminal */}
          <div className="pt-2 sm:pt-4 mt-auto">
            <button
              onClick={() => setIsModalOpen(true)}
              className={`w-full flex items-center justify-center gap-2 px-3 sm:px-4 py-2 sm:py-2.5 text-xs sm:text-sm font-medium rounded-lg transition-colors
                ${server.commands?.length > 0
                  ? 'bg-primary-600 dark:bg-primary-700 text-white hover:bg-primary-700 dark:hover:bg-primary-600'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'}`}
              title={server.commands?.length > 0 ? 'Abrir terminal' : 'Host sem comandos cadastrados'}
              disabled={!server.commands?.length}
            >
              <Terminal className="h-4 w-4" />
              {server.commands?.length > 0 ? 'Abrir Terminal' : 'Sem Comandos'}
            </button>
          </div>
        </div>
      </div>

      <CommandModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        server={server}
      />

      <EditServerModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        server={server}
        onServerUpdated={onServerUpdated}
      />

      <ConfirmModal
        isOpen={isConfirmDeleteOpen}
        onClose={() => setIsConfirmDeleteOpen(false)}
        onConfirm={handleDelete}
        title="Excluir Servidor"
        message="Tem certeza que deseja excluir este servidor? Esta ação não pode ser desfeita."
      />

      <ServerUserAccess
        isOpen={isUserAccessModalOpen}
        onClose={() => setIsUserAccessModalOpen(false)}
        serverId={server.id}
        serverName={server.name}
      />

      <ServerGroupAssignment
        isOpen={isGroupAssignmentOpen}
        onClose={() => setIsGroupAssignmentOpen(false)}
        server={server}
        onServerUpdated={onServerUpdated}
      />

      <ServerBackupModal
        isOpen={isBackupModalOpen}
        onClose={() => setIsBackupModalOpen(false)}
        server={server}
        onServerUpdated={onServerUpdated}
      />
    </>
  )
}

export default ServerCard