-- CreateTable
CREATE TABLE "TerminalTheme" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "backgroundColor" TEXT NOT NULL DEFAULT '#000000',
    "textColor" TEXT NOT NULL DEFAULT '#ffffff',
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TerminalTheme_pkey" PRIMARY KEY ("id")
);

-- AlterTable
ALTER TABLE "User" ADD COLUMN "terminalThemeId" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "TerminalTheme_name_key" ON "TerminalTheme"("name");

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_terminalThemeId_fkey" FOREIGN KEY ("terminalThemeId") REFERENCES "TerminalTheme"("id") ON DELETE SET NULL ON UPDATE CASCADE;
