// Exportações dos componentes comuns com suporte a dark mode
export { Card, CardHeader, CardTitle, CardContent, CardFooter } from './Card'
export { Button, IconButton } from './Button'

// Guia de uso dos componentes com dark mode:

/*
CARD COMPONENT:
===============

import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '../components/common'

<Card>
  <CardHeader>
    <CardTitle>T<PERSON><PERSON><PERSON> do Card</CardTitle>
  </CardHeader>
  <CardContent>
    Conteúdo do card que se adapta automaticamente ao tema dark/light
  </CardContent>
  <CardFooter>
    <Button variant="primary">Ação</Button>
  </CardFooter>
</Card>

BUTTON COMPONENT:
================

import { Button, IconButton } from '../components/common'
import { Plus, Edit, Trash } from 'lucide-react'

// Botão primário
<Button variant="primary" icon={Plus}>
  Adicionar
</Button>

// Botão secundário
<Button variant="secondary" size="lg">
  Cancelar
</Button>

// Botão de perigo
<Button variant="danger" icon={Trash} iconPosition="right">
  Excluir
</Button>

// Botão fantasma
<Button variant="ghost">
  Cancelar
</Button>

// Botão outline
<Button variant="outline">
  Editar
</Button>

// Botão com loading
<Button variant="primary" loading>
  Salvando...
</Button>

// Botão de ícone
<IconButton icon={Edit} aria-label="Editar item" />

CLASSES TAILWIND PARA DARK MODE:
===============================

Backgrounds:
- bg-white dark:bg-gray-800
- bg-gray-50 dark:bg-gray-900
- bg-gray-100 dark:bg-gray-800

Textos:
- text-gray-900 dark:text-gray-100
- text-gray-700 dark:text-gray-200
- text-gray-600 dark:text-gray-300
- text-gray-500 dark:text-gray-400

Bordas:
- border-gray-200 dark:border-gray-700
- border-gray-300 dark:border-gray-600

Hover states:
- hover:bg-gray-100 dark:hover:bg-gray-700
- hover:text-gray-900 dark:hover:text-white

Focus states:
- focus:ring-blue-500 dark:focus:ring-blue-400
- focus:ring-offset-2 dark:focus:ring-offset-gray-800

Shadows:
- shadow-md dark:shadow-gray-900/50

Sempre adicione transition-colors para suavizar as transições entre temas.
*/
