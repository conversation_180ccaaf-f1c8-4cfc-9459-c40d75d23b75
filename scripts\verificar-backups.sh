#!/bin/bash

echo "=== STATUS DOS BACKUPS - $(date) ==="
echo

# Verificar se o container pg_backup está rodando
echo "1. Container pg_backup:"
if docker ps | grep -q pg_backup; then
    echo "   ✓ Container pg_backup está rodando"
else
    echo "   ✗ Container pg_backup NÃO está rodando"
fi

# Verificar backups do container (diretório backups-externos)
echo
echo "2. Backups do container (últimos 3):"
ls -lt /var/www/sem-fronteiras-ssh/backups-externos/ | head -4

# Verificar backups do script cron (diretório /var/backups/sem-fronteiras)
echo
echo "3. Backups do script cron (últimos 3):"
ls -lt /var/backups/sem-fronteiras/*.sql 2>/dev/null | head -3

# Verificar se o cron está configurado
echo
echo "4. Configuração do cron:"
if crontab -l | grep -q backup-externo.sh; then
    echo "   ✓ Cron configurado para backup diário"
else
    echo "   ✗ Cron NÃO configurado"
fi

# Verificar espaço em disco
echo
echo "5. Espaço em disco:"
df -h / | tail -1

# Verificar serviço systemd
echo
echo "6. Serviço sem-fronteiras:"
if systemctl is-enabled sem-fronteiras.service >/dev/null 2>&1; then
    echo "   ✓ Serviço habilitado para inicialização automática"
else
    echo "   ✗ Serviço NÃO habilitado"
fi

if systemctl is-active sem-fronteiras.service >/dev/null 2>&1; then
    echo "   ✓ Serviço está ativo"
else
    echo "   ✗ Serviço NÃO está ativo"
fi

echo
echo "=== FIM DO RELATÓRIO ==="
