export interface TerminalTheme {
  id: string
  name: string
  backgroundColor: string
  textColor: string
  isDefault: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateTerminalThemeDTO {
  name: string
  backgroundColor: string
  textColor: string
  isDefault?: boolean
}

export interface UpdateTerminalThemeDTO {
  name?: string
  backgroundColor?: string
  textColor?: string
  isDefault?: boolean
}
