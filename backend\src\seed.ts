import { CreateServerDTO } from './types/server'
import { randomUUID } from 'crypto'

const basicCommands = [
  {
    name: 'Listar arquivos',
    command: 'ls -la',
    description: 'Lista todos os arquivos e diretórios com detalhes',
    order: 0
  },
  {
    name: 'Mostrar diretório atual',
    command: 'pwd',
    description: 'Exibe o caminho completo do diretório atual',
    order: 1
  },
  {
    name: 'Informações do sistema',
    command: 'uname -a',
    description: 'Mostra informações detalhadas do sistema operacional',
    order: 2
  },
  {
    name: 'Uso do disco',
    command: 'df -h',
    description: 'Mostra o uso do disco em formato legível',
    order: 3
  },
  {
    name: 'Processos em execução',
    command: 'ps -eo pid,ppid,user,%cpu,%mem,stat,start,time,command --sort=-%cpu',
    description: 'Lista os processos em execução, ordenados por uso de CPU',
    order: 4
  },
  {
    name: '<PERSON><PERSON> de memória',
    command: 'free -h',
    description: 'Mostra informações detalhadas sobre o uso de memória',
    order: 5
  },
  {
    name: 'Carga do sistema',
    command: 'uptime',
    description: 'Mostra o tempo de atividade e carga média do sistema',
    order: 6
  }
]

export const initialServers: Omit<CreateServerDTO, 'deviceType'>[] = [
  {
    name: 'Host Linux',
    host: '***********',
    port: 43999,
    username: 'semfronteiras',
    password: '88701181Sem*',
    commands: basicCommands
  }
]