import { FastifyRequest, FastifyReply } from 'fastify'
import { PrismaClient } from '@prisma/client'
import { z } from 'zod'

const prisma = new PrismaClient()

// Schema de validação para criação de tema
const createTerminalThemeSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  backgroundColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Cor de fundo deve ser um hex válido'),
  textColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Cor do texto deve ser um hex válido'),
  isDefault: z.boolean().optional().default(false)
})

// Schema de validação para atualização de tema
const updateTerminalThemeSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório').optional(),
  backgroundColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Cor de fundo deve ser um hex válido').optional(),
  textColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Cor do texto deve ser um hex válido').optional(),
  isDefault: z.boolean().optional()
})

type CreateTerminalThemeRequest = FastifyRequest<{
  Body: z.infer<typeof createTerminalThemeSchema>
}>

type UpdateTerminalThemeRequest = FastifyRequest<{
  Params: { id: string }
  Body: z.infer<typeof updateTerminalThemeSchema>
}>

type DeleteTerminalThemeRequest = FastifyRequest<{
  Params: { id: string }
}>

type GetTerminalThemeRequest = FastifyRequest<{
  Params: { id: string }
}>

export const terminalThemeController = {
  // Listar todos os temas
  async findAll(request: FastifyRequest, reply: FastifyReply) {
    try {
      const themes = await prisma.terminalTheme.findMany({
        orderBy: [
          { isDefault: 'desc' },
          { name: 'asc' }
        ]
      })

      return reply.status(200).send(themes)
    } catch (error) {
      console.error('Erro ao buscar temas do terminal:', error)
      return reply.status(500).send({ 
        error: 'Erro interno do servidor',
        message: 'Não foi possível buscar os temas do terminal'
      })
    }
  },

  // Buscar tema por ID
  async findById(request: GetTerminalThemeRequest, reply: FastifyReply) {
    try {
      const { id } = request.params

      const theme = await prisma.terminalTheme.findUnique({
        where: { id }
      })

      if (!theme) {
        return reply.status(404).send({ 
          error: 'Tema não encontrado',
          message: 'O tema solicitado não existe'
        })
      }

      return reply.status(200).send(theme)
    } catch (error) {
      console.error('Erro ao buscar tema do terminal:', error)
      return reply.status(500).send({ 
        error: 'Erro interno do servidor',
        message: 'Não foi possível buscar o tema do terminal'
      })
    }
  },

  // Criar novo tema (apenas admin)
  async create(request: CreateTerminalThemeRequest, reply: FastifyReply) {
    try {
      const validatedData = createTerminalThemeSchema.parse(request.body)

      // Verificar se já existe um tema com o mesmo nome
      const existingTheme = await prisma.terminalTheme.findUnique({
        where: { name: validatedData.name }
      })

      if (existingTheme) {
        return reply.status(400).send({ 
          error: 'Nome já existe',
          message: 'Já existe um tema com este nome'
        })
      }

      // Se está marcando como padrão, desmarcar outros temas padrão
      if (validatedData.isDefault) {
        await prisma.terminalTheme.updateMany({
          where: { isDefault: true },
          data: { isDefault: false }
        })
      }

      const theme = await prisma.terminalTheme.create({
        data: validatedData
      })

      return reply.status(201).send(theme)
    } catch (error) {
      if (error instanceof z.ZodError) {
        return reply.status(400).send({ 
          error: 'Dados inválidos',
          message: 'Verifique os dados enviados',
          details: error.errors
        })
      }

      console.error('Erro ao criar tema do terminal:', error)
      return reply.status(500).send({ 
        error: 'Erro interno do servidor',
        message: 'Não foi possível criar o tema do terminal'
      })
    }
  },

  // Atualizar tema (apenas admin)
  async update(request: UpdateTerminalThemeRequest, reply: FastifyReply) {
    try {
      const { id } = request.params
      const validatedData = updateTerminalThemeSchema.parse(request.body)

      // Verificar se o tema existe
      const existingTheme = await prisma.terminalTheme.findUnique({
        where: { id }
      })

      if (!existingTheme) {
        return reply.status(404).send({ 
          error: 'Tema não encontrado',
          message: 'O tema solicitado não existe'
        })
      }

      // Verificar se o nome já existe (se estiver sendo alterado)
      if (validatedData.name && validatedData.name !== existingTheme.name) {
        const nameExists = await prisma.terminalTheme.findUnique({
          where: { name: validatedData.name }
        })

        if (nameExists) {
          return reply.status(400).send({ 
            error: 'Nome já existe',
            message: 'Já existe um tema com este nome'
          })
        }
      }

      // Se está marcando como padrão, desmarcar outros temas padrão
      if (validatedData.isDefault) {
        await prisma.terminalTheme.updateMany({
          where: { 
            isDefault: true,
            id: { not: id }
          },
          data: { isDefault: false }
        })
      }

      const updatedTheme = await prisma.terminalTheme.update({
        where: { id },
        data: validatedData
      })

      return reply.status(200).send(updatedTheme)
    } catch (error) {
      if (error instanceof z.ZodError) {
        return reply.status(400).send({ 
          error: 'Dados inválidos',
          message: 'Verifique os dados enviados',
          details: error.errors
        })
      }

      console.error('Erro ao atualizar tema do terminal:', error)
      return reply.status(500).send({ 
        error: 'Erro interno do servidor',
        message: 'Não foi possível atualizar o tema do terminal'
      })
    }
  },

  // Deletar tema (apenas admin)
  async delete(request: DeleteTerminalThemeRequest, reply: FastifyReply) {
    try {
      const { id } = request.params

      // Verificar se o tema existe
      const existingTheme = await prisma.terminalTheme.findUnique({
        where: { id }
      })

      if (!existingTheme) {
        return reply.status(404).send({
          error: 'Tema não encontrado',
          message: 'O tema solicitado não existe'
        })
      }

      // Verificar se é um tema padrão (não pode ser excluído)
      if (existingTheme.isDefault) {
        return reply.status(400).send({
          error: 'Tema padrão',
          message: 'Não é possível excluir um tema padrão'
        })
      }

      // Buscar um tema padrão para reassignar aos usuários
      const defaultTheme = await prisma.terminalTheme.findFirst({
        where: { isDefault: true }
      })

      // Atualizar usuários que usam este tema para usar o tema padrão (ou null)
      await prisma.user.updateMany({
        where: { terminalThemeId: id },
        data: { terminalThemeId: defaultTheme?.id || null }
      })

      // Deletar o tema
      await prisma.terminalTheme.delete({
        where: { id }
      })

      return reply.status(204).send()
    } catch (error) {
      console.error('Erro ao deletar tema do terminal:', error)
      return reply.status(500).send({
        error: 'Erro interno do servidor',
        message: 'Não foi possível deletar o tema do terminal'
      })
    }
  }
}
