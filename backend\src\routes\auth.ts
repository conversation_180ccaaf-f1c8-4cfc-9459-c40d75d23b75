import { FastifyInstance } from 'fastify'
import { z } from 'zod'
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'
import { verifyJWT } from '../middlewares/auth'

const prisma = new PrismaClient()

export async function authRoutes(app: FastifyInstance) {
  app.post('/register', async (request, reply) => {
    const registerSchema = z.object({
      name: z.string(),
      email: z.string().email(),
      password: z.string().min(6),
      isAdmin: z.boolean().optional().default(false),
    })

    const { name, email, password, isAdmin } = registerSchema.parse(request.body)

    const userExists = await prisma.user.findUnique({
      where: { email },
    })

    if (userExists) {
      return reply.status(400).send({ message: 'Usu<PERSON>rio já existe' })
    }

    const hashedPassword = await bcrypt.hash(password, 8)

    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role: isAdmin ? 'ADMIN' : 'USER',
      },
    })

    return reply.status(201).send({ user })
  })

  app.post('/login', async (request, reply) => {
    const loginSchema = z.object({
      email: z.string().email(),
      password: z.string(),
    })

    const { email, password } = loginSchema.parse(request.body)

    const user = await prisma.user.findUnique({
      where: { email },
    })

    if (!user) {
      return reply.status(400).send({ message: 'Credenciais inválidas' })
    }

    const userStatus = await prisma.$queryRaw`SELECT active FROM "User" WHERE id = ${user.id}`
    const isActive = Array.isArray(userStatus) && userStatus.length > 0 && userStatus[0].active === true

    if (!isActive) {
      return reply.status(403).send({ message: 'Usuário desativado. Entre em contato com o administrador.' })
    }

    const passwordMatch = await bcrypt.compare(password, user.password)

    if (!passwordMatch) {
      return reply.status(400).send({ message: 'Credenciais inválidas' })
    }

    const token = app.jwt.sign(
      { 
        id: user.id,
        role: user.role,
      },
      {
        expiresIn: '7d',
      }
    )

    return reply.send({ token })
  })

  app.get('/me', { onRequest: [verifyJWT] }, async (request) => {
    const user = await prisma.user.findUnique({
      where: { id: request.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        terminalThemeId: true,
        terminalTheme: {
          select: {
            id: true,
            name: true,
            backgroundColor: true,
            textColor: true,
            isDefault: true
          }
        }
      },
    })

    return { user }
  })
} 