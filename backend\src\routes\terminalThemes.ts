import { FastifyInstance } from 'fastify'
import { verifyJWT } from '../middlewares/auth'
import { verifyAdmin } from '../middlewares/admin'
import { terminalThemeController } from '../controllers/terminalThemes'

export async function terminalThemeRoutes(fastify: FastifyInstance) {
  // Aplicar middleware de autenticação para todas as rotas
  fastify.addHook('onRequest', verifyJWT)

  // Rotas públicas (para usuários autenticados)
  
  // Listar todos os temas
  fastify.get('/', async (request, reply) => {
    return terminalThemeController.findAll(request, reply)
  })

  // Buscar tema por ID
  fastify.get<{ Params: { id: string } }>('/:id', async (request, reply) => {
    return terminalThemeController.findById(request, reply)
  })

  // Rotas que requerem privilégios de administrador
  fastify.register(async (fastify) => {
    // Aplicar middleware de administrador
    fastify.addHook('onRequest', verifyAdmin)

    // Criar novo tema
    fastify.post<{ Body: any }>('/', async (request, reply) => {
      return terminalThemeController.create(request, reply)
    })

    // Atualizar tema
    fastify.put<{ Params: { id: string }, Body: any }>('/:id', async (request, reply) => {
      return terminalThemeController.update(request, reply)
    })

    // Deletar tema
    fastify.delete<{ Params: { id: string } }>('/:id', async (request, reply) => {
      return terminalThemeController.delete(request, reply)
    })
  })
}
