const fs = require('fs');
const path = require('path');
const { marked } = require('marked');
const puppeteer = require('puppeteer');

// Configuração do marked para melhor renderização
marked.setOptions({
  breaks: true,
  gfm: true
});

// CSS para o PDF
// Função para agrupar conteúdo relacionado
function processContentGrouping(html) {
  // Dividir o HTML em linhas para processamento mais preciso
  const lines = html.split('\n');
  const processedLines = [];
  let i = 0;

  while (i < lines.length) {
    const line = lines[i];

    // Detectar início de seção "Modelos Implementados"
    if (line.includes('<strong>Modelos Implementados:</strong>')) {
      processedLines.push('<div class="content-group">');
      processedLines.push(line);
      i++;

      // Capturar todo o conteúdo até o próximo h3 ou h2
      while (i < lines.length && !lines[i].includes('<h2') && !lines[i].includes('<h3')) {
        // Se encontrar um modelo específico (número + nome), agrupar com seu código
        if (lines[i].match(/^\s*<ol>/) || lines[i].match(/^\s*<li><strong>/)) {
          processedLines.push('<div class="model-section">');

          // Capturar o modelo e seu código associado
          while (i < lines.length &&
                 (!lines[i].includes('<h2') && !lines[i].includes('<h3') &&
                  !lines[i].includes('<strong>Métricas') &&
                  !lines[i].includes('<strong>Entregáveis'))) {
            processedLines.push(lines[i]);
            i++;

            // Se chegou ao final do bloco de código, fechar o grupo
            if (lines[i-1] && lines[i-1].includes('</pre>')) {
              break;
            }
          }
          processedLines.push('</div>'); // Fechar model-section
        } else {
          processedLines.push(lines[i]);
          i++;
        }
      }
      processedLines.push('</div>'); // Fechar content-group
      continue;
    }

    // Detectar outras seções importantes para agrupar
    if (line.includes('<strong>Exemplos de IA em Ação:</strong>') ||
        line.includes('<strong>Entregáveis:</strong>') ||
        line.includes('<strong>Métricas de Performance:</strong>')) {
      processedLines.push('<div class="content-group">');
      processedLines.push(line);
      i++;

      // Capturar conteúdo até próxima seção
      while (i < lines.length &&
             !lines[i].includes('<h2') && !lines[i].includes('<h3') &&
             !lines[i].includes('<hr') &&
             !lines[i].includes('<strong>Entregáveis:') &&
             !lines[i].includes('<strong>Exemplos de IA')) {
        processedLines.push(lines[i]);
        i++;
      }
      processedLines.push('</div>');
      continue;
    }

    // Adicionar quebras de página estratégicas
    if (line.includes('🔍 **DETALHAMENTO TÉCNICO**') ||
        line.includes('🖥️ **INFRAESTRUTURA LOCAL NECESSÁRIA**') ||
        line.includes('🚀 **CAPACIDADES AVANÇADAS DA IA**')) {
      processedLines.push('<div class="page-break"></div>');
    }

    processedLines.push(line);
    i++;
  }

  return processedLines.join('\n');
}

const pdfStyles = `
<style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: #2d3748;
    background: #ffffff;
    font-size: 14px;
  }
  
  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 60px;
  }
  
  /* Cabeçalho */
  h1 {
    color: #1a365d;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
    text-align: center;
    border-bottom: 3px solid #3182ce;
    padding-bottom: 15px;
  }
  
  h2 {
    color: #2d3748;
    font-size: 20px;
    font-weight: 600;
    margin: 30px 0 15px 0;
    padding: 12px 20px;
    background: linear-gradient(135deg, #e6f3ff 0%, #f0f8ff 100%);
    border-left: 4px solid #3182ce;
    border-radius: 0 8px 8px 0;
  }
  
  h3 {
    color: #2d3748;
    font-size: 16px;
    font-weight: 600;
    margin: 20px 0 10px 0;
    padding: 8px 15px;
    background: #f7fafc;
    border-left: 3px solid #4299e1;
    border-radius: 0 4px 4px 0;
  }
  
  h4 {
    color: #4a5568;
    font-size: 14px;
    font-weight: 600;
    margin: 15px 0 8px 0;
  }
  
  /* Texto */
  p {
    margin-bottom: 12px;
    text-align: justify;
  }
  
  strong {
    color: #2d3748;
    font-weight: 600;
  }
  
  em {
    color: #4a5568;
    font-style: italic;
  }
  
  /* Listas */
  ul, ol {
    margin: 15px 0;
    padding-left: 25px;
  }
  
  li {
    margin-bottom: 6px;
  }
  
  /* Tabelas */
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }
  
  th {
    background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
    color: white;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 13px;
  }
  
  td {
    padding: 12px;
    border-bottom: 1px solid #e2e8f0;
    font-size: 13px;
  }
  
  tr:nth-child(even) {
    background-color: #f8fafc;
  }
  
  tr:hover {
    background-color: #edf2f7;
  }
  
  /* Código */
  code {
    background: #f1f5f9;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    color: #e53e3e;
  }
  
  pre {
    background: #1a202c;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 15px 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.5;
  }
  
  pre code {
    background: none;
    padding: 0;
    color: inherit;
  }
  
  /* Separadores */
  hr {
    border: none;
    height: 2px;
    background: linear-gradient(90deg, #3182ce, #4299e1, #3182ce);
    margin: 30px 0;
    border-radius: 1px;
  }
  
  /* Emojis e ícones */
  .emoji {
    font-size: 16px;
    margin-right: 8px;
  }
  
  /* Destaque para valores */
  .price {
    color: #38a169;
    font-weight: 700;
    font-size: 16px;
  }
  
  /* Caixas de destaque */
  .highlight-box {
    background: linear-gradient(135deg, #e6fffa 0%, #f0fff4 100%);
    border: 1px solid #38a169;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
  }
  
  /* Quebras de página e controle de elementos */
  .page-break {
    page-break-before: always;
  }

  /* Evitar quebras de página dentro de elementos */
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
    page-break-inside: avoid;
  }

  table {
    page-break-inside: avoid;
    page-break-before: auto;
    page-break-after: auto;
  }

  pre {
    page-break-inside: avoid;
    page-break-before: auto;
    page-break-after: auto;
  }

  .highlight-box {
    page-break-inside: avoid;
    page-break-before: auto;
    page-break-after: auto;
  }

  /* Manter parágrafos juntos quando possível */
  p {
    orphans: 3;
    widows: 3;
  }

  /* Manter listas unidas */
  ul, ol {
    page-break-inside: avoid;
  }

  /* Evitar quebras órfãs em títulos - manter títulos com conteúdo seguinte */
  h2, h3, h4 {
    page-break-after: avoid;
    page-break-inside: avoid;
  }

  h2 + p, h2 + ul, h2 + ol, h2 + pre, h2 + table,
  h3 + p, h3 + ul, h3 + ol, h3 + pre, h3 + table,
  h4 + p, h4 + ul, h4 + ol, h4 + pre, h4 + table {
    page-break-before: avoid;
  }

  /* Seções específicas que não devem ser quebradas */
  .no-break {
    page-break-inside: avoid;
  }

  /* Grupos de conteúdo relacionado */
  .content-group {
    page-break-inside: avoid;
    margin-bottom: 20px;
  }

  /* Manter modelos implementados juntos */
  .model-section {
    page-break-inside: avoid;
    margin-bottom: 25px;
  }
  
  /* Rodapé */
  .footer {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 2px solid #e2e8f0;
    text-align: center;
    color: #718096;
    font-size: 12px;
  }
  
  /* Ajustes para impressão */
  @media print {
    body {
      font-size: 12px;
    }
    
    .container {
      padding: 20px 40px;
    }
    
    h1 {
      font-size: 24px;
    }
    
    h2 {
      font-size: 18px;
    }
    
    h3 {
      font-size: 14px;
    }
  }
</style>
`;

async function generatePDF() {
  try {
    console.log('📄 Iniciando geração do PDF do orçamento...');
    
    // Ler o arquivo markdown
    const markdownPath = path.join(__dirname, 'orcamento_ia_local.md');
    const markdownContent = fs.readFileSync(markdownPath, 'utf8');
    
    console.log('📖 Arquivo markdown carregado');
    
    // Converter markdown para HTML
    let htmlContent = marked(markdownContent);

    // Processar o HTML para agrupar conteúdo relacionado
    htmlContent = processContentGrouping(htmlContent);
    
    // Criar HTML completo com estilos
    const fullHtml = `
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Orçamento - Sistema de Monitoramento Preditivo com IA</title>
      ${pdfStyles}
    </head>
    <body>
      <div class="container">
        ${htmlContent}
      </div>
    </body>
    </html>
    `;
    
    console.log('🎨 HTML formatado criado');
    
    // Inicializar Puppeteer
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Configurar o conteúdo da página
    await page.setContent(fullHtml, { waitUntil: 'networkidle0' });
    
    console.log('🌐 Página carregada no navegador');
    
    // Gerar PDF
    const pdfPath = path.join(__dirname, 'orcamento_ia_local.pdf');
    await page.pdf({
      path: pdfPath,
      format: 'A4',
      printBackground: true,
      margin: {
        top: '20mm',
        right: '15mm',
        bottom: '20mm',
        left: '15mm'
      },
      // Configurações para melhor controle de quebra de página
      preferCSSPageSize: true,
      displayHeaderFooter: false
    });
    
    await browser.close();
    
    console.log('✅ PDF gerado com sucesso!');
    console.log(`📁 Arquivo salvo em: ${pdfPath}`);
    
    return pdfPath;
    
  } catch (error) {
    console.error('❌ Erro ao gerar PDF:', error);
    throw error;
  }
}

// Função para gerar PDF do contrato
async function generateContractPDF() {
  try {
    console.log('📄 Iniciando geração do PDF do contrato...');

    // Ler o arquivo markdown do contrato
    const contractPath = path.join(__dirname, 'contrato_sistema_ia_preditiva.md');
    const markdownContent = fs.readFileSync(contractPath, 'utf8');

    console.log('📖 Arquivo do contrato carregado');

    // Converter markdown para HTML
    let htmlContent = marked(markdownContent);

    // Processar o HTML para melhor formatação de contrato
    htmlContent = htmlContent
      .replace(/<h1>/g, '<h1 style="text-align: center; color: #1a365d; border-bottom: 3px solid #3182ce; padding-bottom: 20px;">')
      .replace(/<h2>/g, '<h2 style="color: #2d3748; background: linear-gradient(135deg, #e6f3ff 0%, #f0f8ff 100%); padding: 15px; border-left: 4px solid #3182ce; margin-top: 30px;">')
      .replace(/\*\*CLÁUSULA/g, '<div class="page-break-avoid"><strong>CLÁUSULA')
      .replace(/\*\*Parágrafo/g, '<strong>Parágrafo');

    // Criar HTML completo com estilos específicos para contrato
    const contractHtml = `
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Contrato - Sistema de Monitoramento Preditivo com IA</title>
      ${pdfStyles}
      <style>
        .page-break-avoid {
          page-break-inside: avoid;
          margin-bottom: 20px;
        }

        .contract-clause {
          margin-bottom: 25px;
          page-break-inside: avoid;
        }

        .signature-section {
          margin-top: 40px;
          page-break-inside: avoid;
        }

        .signature-line {
          border-bottom: 1px solid #000;
          width: 300px;
          margin: 20px 0 5px 0;
        }
      </style>
    </head>
    <body>
      <div class="container">
        ${htmlContent}
      </div>
    </body>
    </html>
    `;

    console.log('🎨 HTML do contrato formatado');

    // Inicializar Puppeteer
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Configurar o conteúdo da página
    await page.setContent(contractHtml, { waitUntil: 'networkidle0' });

    console.log('🌐 Página do contrato carregada no navegador');

    // Gerar PDF do contrato
    const contractPdfPath = path.join(__dirname, 'contrato_sistema_ia_preditiva.pdf');
    await page.pdf({
      path: contractPdfPath,
      format: 'A4',
      printBackground: true,
      margin: {
        top: '25mm',
        right: '20mm',
        bottom: '25mm',
        left: '20mm'
      },
      preferCSSPageSize: true,
      displayHeaderFooter: false
    });

    await browser.close();

    console.log('✅ PDF do contrato gerado com sucesso!');
    console.log(`📁 Arquivo salvo em: ${contractPdfPath}`);

    return contractPdfPath;

  } catch (error) {
    console.error('❌ Erro ao gerar PDF do contrato:', error);
    throw error;
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.includes('--contrato')) {
    generateContractPDF()
      .then((pdfPath) => {
        console.log(`\n🎉 PDF do contrato gerado com sucesso!`);
        console.log(`📄 Arquivo: ${pdfPath}`);
      })
      .catch((error) => {
        console.error('\n💥 Falha na geração do PDF do contrato:', error.message);
        process.exit(1);
      });
  } else {
    generatePDF()
      .then((pdfPath) => {
        console.log(`\n🎉 PDF do orçamento gerado com sucesso!`);
        console.log(`📄 Arquivo: ${pdfPath}`);
      })
      .catch((error) => {
        console.error('\n💥 Falha na geração do PDF:', error.message);
        process.exit(1);
      });
  }
}

module.exports = { generatePDF, generateContractPDF };
