import React, { createContext, useContext, useEffect, useState } from 'react'
import { api } from '../lib/axios'
import axios from 'axios'
import { TerminalTheme } from '../types/terminalTheme'

interface User {
  id: string
  name: string
  email: string
  role: 'ADMIN' | 'USER'
  terminalThemeId?: string | null
  terminalTheme?: TerminalTheme | null
}

interface AuthContextData {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => void
  updateUserTheme: (themeId: string | null) => Promise<void>
  updateUserProfile: (userData: { name?: string; email?: string }) => void
}

const AuthContext = createContext({} as AuthContextData)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user

  useEffect(() => {
    const token = localStorage.getItem('sem-fronteiras:token')

    if (token) {
      api.get('/me')
        .then(response => {
          setUser(response.data.user)
        })
        .catch(() => {
          localStorage.removeItem('sem-fronteiras:token')
        })
        .finally(() => {
          setIsLoading(false)
        })
    } else {
      setIsLoading(false)
    }
  }, [])

  async function signIn(email: string, password: string) {
    try {
      const response = await api.post('/login', {
        email,
        password,
      })

      const { token } = response.data

      localStorage.setItem('sem-fronteiras:token', token)

      const { data } = await api.get('/me')
      setUser(data.user)
    } catch (err) {
      localStorage.removeItem('sem-fronteiras:token')
      
      // Verificar se é um erro de usuário desativado (código 403)
      if (axios.isAxiosError(err) && err.response) {
        if (err.response.status === 403) {
          throw new Error(err.response.data.message || 'Usuário desativado. Entre em contato com o administrador.')
        }
      }
      
      throw new Error('Credenciais inválidas')
    }
  }

  function signOut() {
    setUser(null)
    localStorage.removeItem('sem-fronteiras:token')
  }

  async function updateUserTheme(themeId: string | null) {
    try {
      await api.patch('/api/users/profile/terminal-theme', { terminalThemeId: themeId })

      // Atualizar o usuário no contexto
      if (user) {
        const updatedUser = { ...user, terminalThemeId: themeId }

        // Se themeId for null, remover o tema
        if (themeId === null) {
          updatedUser.terminalTheme = null
        } else {
          // Buscar informações do tema
          try {
            const themeResponse = await api.get(`/api/terminal-themes/${themeId}`)
            updatedUser.terminalTheme = themeResponse.data
          } catch (error) {
            console.error('Erro ao buscar tema:', error)
          }
        }

        setUser(updatedUser)
      }
    } catch (error) {
      console.error('Erro ao atualizar tema do usuário:', error)
      throw error
    }
  }

  function updateUserProfile(userData: { name?: string; email?: string }) {
    if (user) {
      const updatedUser = { ...user, ...userData }
      setUser(updatedUser)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <AuthContext.Provider value={{ user, isAuthenticated, isLoading, signIn, signOut, updateUserTheme, updateUserProfile }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)

  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }

  return context
} 