/**
 * Configurações de proteção contra loops infinitos
 * Centraliza todas as constantes relacionadas à prevenção de loops no sistema SSH
 */

export const LOOP_PROTECTION_CONFIG = {
  // Circuit Breaker - Proteção contra falhas repetidas de conexão
  CIRCUIT_BREAKER: {
    FAILURE_THRESHOLD: 5, // Número máximo de falhas antes de ativar o circuit breaker
    TIMEOUT_MS: 300000, // 5 minutos - tempo para resetar o circuit breaker
    DESCRIPTION: 'Previne tentativas excessivas de conexão após falhas repetidas'
  },

  // Connection Lock - Proteção contra múltiplas conexões simultâneas
  CONNECTION_LOCK: {
    MAX_WAIT_TIME_MS: 30000, // 30 segundos máximo para aguardar liberação do lock
    CHECK_INTERVAL_MS: 500, // Intervalo entre verificações do lock
    DESCRIPTION: 'Previne deadlocks em tentativas de conexão simultâneas'
  },

  // Command Completion - Proteção contra verificações infinitas de conclusão
  COMMAND_COMPLETION: {
    BASE_MAX_CHECKS: 80, // Número base de verificações permitidas (aumentado de 50 para 80)
    CHECKS_PER_COMMAND: 15, // Verificações adicionais por comando (aumentado de 10 para 15)
    MIN_MAX_CHECKS: 80, // Mínimo absoluto de verificações (aumentado de 50 para 80)
    MAX_MAX_CHECKS: 600, // Máximo absoluto de verificações (aumentado de 500 para 600)
    CHECK_INTERVAL_MS: 500, // Intervalo entre verificações
    DESCRIPTION: 'Previne loops infinitos na verificação de conclusão de comandos'
  },

  // Session Management - Proteção contra sessões órfãs
  SESSION_MANAGEMENT: {
    MAX_SESSION_TIME_MS: 600000, // 10 minutos - tempo máximo de sessão
    FORCE_CLEANUP_MEMORY_THRESHOLD_MB: 500, // 500MB - limite de memória para limpeza forçada
    RESOURCE_MONITOR_INTERVAL_MS: 120000, // 2 minutos - intervalo de monitoramento
    DESCRIPTION: 'Previne acúmulo de sessões órfãs e vazamentos de memória'
  },

  // Reconnection - Proteção contra reconexões infinitas
  RECONNECTION: {
    MAX_ATTEMPTS: 3, // Número máximo de tentativas de reconexão
    DELAY_MS: 5000, // 5 segundos - atraso entre tentativas
    EXPONENTIAL_BACKOFF: true, // Usar backoff exponencial
    DESCRIPTION: 'Previne loops infinitos de reconexão'
  },

  // Device Specific - Configurações específicas por tipo de dispositivo
  DEVICE_SPECIFIC: {
    NOKIA: {
      DISABLE_AUTO_RECONNECT: true, // Desabilitar reconexão automática
      MAX_COMPLETION_CHECKS_MULTIPLIER: 10, // Multiplicador para verificações de conclusão
      DESCRIPTION: 'Configurações específicas para dispositivos Nokia'
    },
    MIKROTIK: {
      KEEPALIVE_INTERVAL_MS: 30000, // 30 segundos - intervalo de keepalive manual
      API_TIMEOUT_MS: 3000, // 3 segundos - timeout para API
      MAX_RETRY_ATTEMPTS: 3, // Máximo de tentativas de retry
      DESCRIPTION: 'Configurações específicas para dispositivos Mikrotik'
    },
    HUAWEI: {
      EXTENDED_TIMEOUTS: true, // Usar timeouts estendidos
      CONSERVATIVE_SETTINGS: true, // Usar configurações conservadoras
      DESCRIPTION: 'Configurações específicas para dispositivos Huawei'
    }
  },

  // Timeouts - Configurações gerais de timeout
  TIMEOUTS: {
    BASE_TIMEOUT_MS: 60000, // 1 minuto - timeout base
    TIMEOUT_PER_COMMAND_MS: 10000, // 10 segundos por comando
    MAX_TIMEOUT_MS: 600000, // 10 minutos - timeout máximo absoluto
    INACTIVITY_TIMEOUT_MS: 3000, // 3 segundos - timeout de inatividade
    DESCRIPTION: 'Timeouts gerais para operações SSH'
  }
};

/**
 * Calcula o número máximo de verificações de conclusão baseado no número de comandos
 * @param commandCount Número de comandos
 * @returns Número máximo de verificações permitidas
 */
export function calculateMaxCompletionChecks(commandCount: number = 1): number {
  const config = LOOP_PROTECTION_CONFIG.COMMAND_COMPLETION;
  const calculated = config.BASE_MAX_CHECKS + (commandCount * config.CHECKS_PER_COMMAND);
  return Math.min(Math.max(calculated, config.MIN_MAX_CHECKS), config.MAX_MAX_CHECKS);
}

/**
 * Calcula o atraso de reconexão com backoff exponencial
 * @param attempt Número da tentativa (começando em 1)
 * @returns Atraso em milissegundos
 */
export function calculateReconnectionDelay(attempt: number): number {
  const config = LOOP_PROTECTION_CONFIG.RECONNECTION;
  if (!config.EXPONENTIAL_BACKOFF) {
    return config.DELAY_MS;
  }
  
  // Backoff exponencial: delay * (2 ^ (attempt - 1))
  // Limitado a um máximo de 60 segundos
  const exponentialDelay = config.DELAY_MS * Math.pow(2, attempt - 1);
  return Math.min(exponentialDelay, 60000);
}

/**
 * Verifica se o uso de memória está acima do limite
 * @returns true se a memória está acima do limite
 */
export function isMemoryUsageHigh(): boolean {
  const memUsage = process.memoryUsage();
  const heapUsedMB = memUsage.heapUsed / 1024 / 1024;
  return heapUsedMB > LOOP_PROTECTION_CONFIG.SESSION_MANAGEMENT.FORCE_CLEANUP_MEMORY_THRESHOLD_MB;
}

/**
 * Obtém configurações específicas para um tipo de dispositivo
 * @param deviceType Tipo do dispositivo
 * @returns Configurações específicas ou null se não encontradas
 */
export function getDeviceSpecificConfig(deviceType: string): any {
  const upperDeviceType = deviceType.toUpperCase();
  return LOOP_PROTECTION_CONFIG.DEVICE_SPECIFIC[upperDeviceType as keyof typeof LOOP_PROTECTION_CONFIG.DEVICE_SPECIFIC] || null;
}

/**
 * Log das configurações de proteção ativas
 */
export function logProtectionConfig(): void {
  console.log('🛡️ Configurações de Proteção contra Loops Infinitos:');
  console.log(`   Circuit Breaker: ${LOOP_PROTECTION_CONFIG.CIRCUIT_BREAKER.FAILURE_THRESHOLD} falhas / ${LOOP_PROTECTION_CONFIG.CIRCUIT_BREAKER.TIMEOUT_MS}ms`);
  console.log(`   Connection Lock: ${LOOP_PROTECTION_CONFIG.CONNECTION_LOCK.MAX_WAIT_TIME_MS}ms timeout`);
  console.log(`   Command Completion: ${LOOP_PROTECTION_CONFIG.COMMAND_COMPLETION.BASE_MAX_CHECKS}-${LOOP_PROTECTION_CONFIG.COMMAND_COMPLETION.MAX_MAX_CHECKS} verificações`);
  console.log(`   Session Management: ${LOOP_PROTECTION_CONFIG.SESSION_MANAGEMENT.MAX_SESSION_TIME_MS}ms / ${LOOP_PROTECTION_CONFIG.SESSION_MANAGEMENT.FORCE_CLEANUP_MEMORY_THRESHOLD_MB}MB`);
  console.log(`   Reconnection: ${LOOP_PROTECTION_CONFIG.RECONNECTION.MAX_ATTEMPTS} tentativas`);
}
