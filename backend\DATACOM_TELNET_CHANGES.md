# Mudanças para Implementar Telnet no DatacomExecutor

## Resumo
Implementada a mudança do DatacomExecutor para usar Telnet ao invés de SSH, conforme solicitado pelo usuário.

## Arquivos Modificados

### 1. `backend/package.json`
- **Adicionado**: Dependência `telnet-client` para suporte a conexões Telnet

### 2. `backend/src/services/ssh/executors/datacomExecutor.ts`
- **Mudança Principal**: Reescrito completamente para usar Telnet ao invés de SSH
- **Removido**: Herança de `BaseExecutor`
- **Removido**: Dependência de `NodeSSH`
- **Adicionado**: Uso da biblioteca `telnet-client`
- **Adicionado**: Construtor que aceita parâmetros de conexão Telnet (host, porta, usuário, senha)
- **Modificado**: Métodos de conexão e execução de comandos para usar Telnet
- **Mantido**: Sistema de fila de comandos e timeouts dinâmicos
- **Mantido**: Suporte a comandos multilinhas

### 3. `backend/src/services/ssh/executors/index.ts`
- **Modificado**: Função `makeExecutor` para aceitar parâmetros de servidor
- **Adicionado**: Tratamento especial para dispositivos DATACOM
- **Adicionado**: Validação de parâmetros necessários para Telnet

### 4. `backend/src/services/ssh/SSHService.ts`
- **Modificado**: Método `connect` para tratar dispositivos Datacom separadamente
- **Modificado**: Método `isConnectedAndReady` para considerar conexões Telnet
- **Modificado**: Método `disconnect` para limpar conexões Telnet
- **Modificado**: Método `executeCommand` para usar verificação correta de conexão

## Funcionalidades Implementadas

### Conexão Telnet
- Conexão automática via Telnet na porta 23 (padrão)
- Suporte a autenticação com usuário e senha
- Configuração de prompts e timeouts otimizados para Datacom

### Execução de Comandos
- Execução de comandos individuais via Telnet
- Suporte a comandos multilinhas
- Sistema de fila para evitar sobrecarga do dispositivo
- Timeouts dinâmicos baseados na complexidade dos comandos

### Gerenciamento de Conexão
- Keepalive automático para manter conexão ativa
- Reconexão automática em caso de falha
- Limpeza adequada de recursos ao desconectar

### Compatibilidade
- Interface compatível com outros executores
- Integração transparente com o SSHService
- Suporte a todos os tipos de comando existentes

## Benefícios da Mudança

1. **Melhor Compatibilidade**: Telnet é mais compatível com dispositivos Datacom antigos
2. **Menos Problemas de Autenticação**: Evita problemas de SSH com dispositivos legados
3. **Configuração Simplificada**: Não requer configuração de chaves SSH
4. **Protocolo Nativo**: Muitos dispositivos Datacom foram projetados para Telnet

## Uso

O DatacomExecutor agora é criado automaticamente quando um servidor tem `deviceType: 'DATACOM'`. 
A conexão Telnet é estabelecida usando as informações do servidor (host, porta, usuário, senha).

## Testes

Criado arquivo `test-datacom-telnet.js` para validar a implementação básica.

## Notas Importantes

- A mudança é específica apenas para dispositivos DATACOM
- Outros tipos de dispositivo continuam usando SSH normalmente
- A interface pública permanece a mesma para compatibilidade
- Logs foram atualizados para indicar uso de Telnet
