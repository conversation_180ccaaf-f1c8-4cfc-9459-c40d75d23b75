import React, { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Role } from '../types/user'
import { User, Mail, Lock, Shield } from 'lucide-react'

// Esquema base para todos os formulários
const baseUserFormSchema = {
  name: z.string().min(3, 'Nome deve ter no mínimo 3 caracteres'),
  email: z.string().email('E-mail inválido'),
  role: z.enum(['ADMIN', 'USER'] as const).optional()
}

// Esquema para perfil próprio (sem email)
const ownProfileFormSchema = {
  name: z.string().min(3, 'Nome deve ter no mínimo 3 caracteres'),
  password: z.string().optional()
}

// Esquema para criação (com senha obrigatória)
const createUserFormSchema = z.object({
  ...baseUserFormSchema,
  password: z.string().min(6, 'Senha deve ter no mínimo 6 caracteres')
})

// Esquema para edição (com senha realmente opcional)
const updateUserFormSchema = z.object({
  ...baseUserFormSchema,
  password: z.string().optional()
})

// Esquema para edição do próprio perfil
const updateOwnProfileFormSchema = z.object({
  ...ownProfileFormSchema
})

// Tipos derivados dos esquemas
type CreateUserFormData = z.infer<typeof createUserFormSchema>
type UpdateUserFormData = z.infer<typeof updateUserFormSchema>
export type UserFormData = CreateUserFormData | UpdateUserFormData

interface UserFormProps {
  onSubmit: (data: UserFormData) => void
  initialData?: Partial<UserFormData> | null
  onCancel?: () => void
  isEditing?: boolean
  isAdmin?: boolean
  isOwnProfile?: boolean // Nova prop para indicar se é o próprio perfil
}

export function UserForm({ onSubmit, initialData, onCancel, isEditing = false, isAdmin = true, isOwnProfile = false }: UserFormProps) {
  // Escolhe o esquema apropriado com base no modo (criação, edição ou próprio perfil)
  const formSchema = isOwnProfile
    ? updateOwnProfileFormSchema
    : isEditing
      ? updateUserFormSchema
      : createUserFormSchema

  // Preparar valores iniciais baseado no tipo de formulário
  const getDefaultValues = () => {
    if (!initialData) return undefined

    if (isOwnProfile) {
      // Para perfil próprio, incluir apenas name (senha sempre vazia)
      return {
        name: initialData.name || '',
        password: ''
      }
    }

    // Para outros casos, usar todos os dados
    return {
      ...initialData,
      password: '' // Senha sempre vazia para edição
    }
  }

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<UserFormData>({
    resolver: zodResolver(formSchema) as any, // Usamos any aqui para evitar problemas de tipagem
    defaultValues: getDefaultValues()
  })

  // Atualizar o formulário quando initialData mudar
  useEffect(() => {
    if (initialData) {
      const values = getDefaultValues()
      reset(values)
    }
  }, [initialData, isOwnProfile, reset])

  const handleFormSubmit = (data: UserFormData) => {
    let submitData = { ...data }

    // Se estiver editando e a senha estiver vazia, remova o campo
    if (isEditing && (!data.password || data.password === '')) {
      const { password, ...restData } = submitData as UpdateUserFormData
      submitData = restData
    }

    // Se for o próprio perfil, garantir que o email não seja enviado
    if (isOwnProfile) {
      const { email, ...restData } = submitData as any
      submitData = restData
    }

    onSubmit(submitData)
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      <div className="space-y-4">
        {/* Campo Nome */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Nome
          </label>
          <div className="mt-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <User className="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
            <input
              type="text"
              id="name"
              {...register('name')}
              placeholder="Nome completo"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>
          {errors.name && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name?.message?.toString()}</p>
          )}
        </div>

        {/* Campo E-mail */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            E-mail
            {isOwnProfile && (
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">(não pode ser alterado)</span>
            )}
          </label>
          <div className="mt-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Mail className={`h-5 w-5 ${isOwnProfile ? 'text-gray-300 dark:text-gray-600' : 'text-gray-400 dark:text-gray-500'}`} />
            </div>
            <input
              type="email"
              id="email"
              {...(isOwnProfile ? {} : register('email'))}
              value={isOwnProfile ? (initialData?.email || '') : undefined}
              placeholder="<EMAIL>"
              disabled={isOwnProfile}
              readOnly={isOwnProfile}
              className={`block w-full pl-10 pr-3 py-2 border rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none sm:text-sm ${
                isOwnProfile
                  ? 'border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
              }`}
            />
          </div>
          {errors.email && !isOwnProfile && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email?.message?.toString()}</p>
          )}
        </div>

        {/* Campo Senha */}
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {isEditing ? 'Senha (deixe em branco para manter a atual)' : 'Senha'}
          </label>
          <div className="mt-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Lock className="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
            <input
              type="password"
              id="password"
              {...register('password')}
              placeholder={isEditing ? "••••••" : "Mínimo 6 caracteres"}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.password?.message?.toString()}</p>
          )}
        </div>

        {/* Campo Nível de Acesso */}
        {isAdmin && (
          <div>
            <label htmlFor="role" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Nível de Acesso
            </label>
            <div className="mt-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Shield className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              </div>
              <select
                id="role"
                {...register('role')}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                <option value="USER">Usuário</option>
                <option value="ADMIN">Administrador</option>
              </select>
            </div>
            {errors.role && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.role?.message?.toString()}</p>
            )}
          </div>
        )}
      </div>

      <div className="flex justify-end space-x-2">
        {onCancel && (
          <button
            type="button"
            onClick={onCancel}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-blue-500 dark:focus:ring-blue-400"
          >
            Cancelar
          </button>
        )}
        <button
          type="submit"
          disabled={isSubmitting}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-blue-500 dark:focus:ring-blue-400 disabled:opacity-50"
        >
          {isSubmitting ? 'Salvando...' : isEditing ? 'Atualizar' : 'Criar'}
        </button>
      </div>
    </form>
  )
} 