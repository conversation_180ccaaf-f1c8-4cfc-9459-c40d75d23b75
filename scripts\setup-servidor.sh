#!/bin/bash

# Script para configuração automática do sistema de backup e inicialização
# em um novo servidor Linux
#
# Uso: sudo ./scripts/setup-servidor.sh
#
# Este script deve ser executado como root ou com sudo

set -e  # Parar execução em caso de erro

echo "=========================================="
echo "  SETUP AUTOMÁTICO - SEM FRONTEIRAS SSH"
echo "=========================================="
echo

# Verificar se está sendo executado como root
if [ "$(id -u)" -ne 0 ]; then
    echo "❌ ERRO: Este script deve ser executado como root ou com sudo"
    echo "   Uso: sudo ./scripts/setup-servidor.sh"
    exit 1
fi

# Verificar se o Docker está instalado
if ! command -v docker &> /dev/null; then
    echo "❌ ERRO: Docker não está instalado"
    echo "   Instale o Docker primeiro: https://docs.docker.com/engine/install/"
    exit 1
fi

# Verificar se o docker-compose está instalado
if ! command -v docker-compose &> /dev/null; then
    echo "❌ ERRO: docker-compose não está instalado"
    echo "   Instale o docker-compose primeiro"
    exit 1
fi

# Verificar se estamos no diretório correto
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ ERRO: Arquivo docker-compose.yml não encontrado"
    echo "   Execute este script a partir do diretório raiz do projeto"
    exit 1
fi

echo "✅ Verificações iniciais concluídas"
echo

# 1. Habilitar e iniciar Docker
echo "🔧 1. Configurando Docker..."
systemctl enable docker
systemctl start docker
echo "   ✅ Docker habilitado e iniciado"
echo

# 2. Configurar backup via cron
echo "🔧 2. Configurando backup diário via cron..."
if [ -f "scripts/configurar-backup-diario.sh" ]; then
    chmod +x scripts/configurar-backup-diario.sh
    ./scripts/configurar-backup-diario.sh
    echo "   ✅ Backup diário configurado"
else
    echo "   ⚠️  Script configurar-backup-diario.sh não encontrado"
fi
echo

# 3. Instalar serviço systemd
echo "🔧 3. Instalando serviço de inicialização automática..."
if [ -f "scripts/sem-fronteiras.service" ]; then
    cp scripts/sem-fronteiras.service /etc/systemd/system/
    systemctl daemon-reload
    systemctl enable sem-fronteiras.service
    echo "   ✅ Serviço systemd instalado e habilitado"
else
    echo "   ⚠️  Arquivo sem-fronteiras.service não encontrado"
fi
echo

# 4. Criar diretórios necessários
echo "🔧 4. Criando diretórios de backup..."
mkdir -p backups-externos
mkdir -p /var/backups/sem-fronteiras
chmod 755 backups-externos
chmod 755 /var/backups/sem-fronteiras
echo "   ✅ Diretórios criados"
echo

# 5. Configurar permissões dos scripts
echo "🔧 5. Configurando permissões dos scripts..."
chmod +x scripts/*.sh 2>/dev/null || true
echo "   ✅ Permissões configuradas"
echo

# 6. Iniciar containers
echo "🔧 6. Iniciando containers Docker..."
docker-compose down 2>/dev/null || true  # Parar containers existentes
docker-compose up -d
echo "   ✅ Containers iniciados"
echo

# 7. Aguardar containers iniciarem
echo "🔧 7. Aguardando containers iniciarem..."
sleep 10
echo

# 8. Verificar configuração
echo "🔧 8. Verificando configuração..."
echo
if [ -f "scripts/verificar-backups.sh" ]; then
    chmod +x scripts/verificar-backups.sh
    ./scripts/verificar-backups.sh
else
    echo "   ⚠️  Script verificar-backups.sh não encontrado"
    echo "   Verificando manualmente..."
    
    # Verificação manual básica
    echo "   Containers rodando:"
    docker ps --format "table {{.Names}}\t{{.Status}}"
    
    echo
    echo "   Serviço systemd:"
    systemctl is-enabled sem-fronteiras.service && echo "   ✅ Habilitado" || echo "   ❌ Não habilitado"
    systemctl is-active sem-fronteiras.service && echo "   ✅ Ativo" || echo "   ❌ Não ativo"
    
    echo
    echo "   Cron configurado:"
    crontab -l | grep -q backup && echo "   ✅ Configurado" || echo "   ❌ Não configurado"
fi

echo
echo "=========================================="
echo "  ✅ CONFIGURAÇÃO CONCLUÍDA COM SUCESSO!"
echo "=========================================="
echo
echo "📋 RESUMO DO QUE FOI CONFIGURADO:"
echo "   ✅ Docker habilitado para inicialização automática"
echo "   ✅ Backup diário via cron (meia-noite)"
echo "   ✅ Serviço systemd para inicialização automática"
echo "   ✅ Containers Docker iniciados"
echo "   ✅ Backup automático via container (24h)"
echo
echo "📍 LOCAIS DOS BACKUPS:"
echo "   📁 Container: $(pwd)/backups-externos/"
echo "   📁 Cron: /var/backups/sem-fronteiras/"
echo
echo "🔍 COMANDOS ÚTEIS:"
echo "   Verificar status: ./scripts/verificar-backups.sh"
echo "   Ver containers: docker ps"
echo "   Ver logs backup: docker logs pg_backup"
echo "   Status serviço: systemctl status sem-fronteiras.service"
echo
echo "🌐 ACESSO À APLICAÇÃO:"
echo "   Frontend: http://localhost:5173"
echo "   Backend: http://localhost:3000"
echo
echo "⚠️  IMPORTANTE:"
echo "   - Os backups serão executados automaticamente"
echo "   - O sistema reiniciará automaticamente após reboot"
echo "   - Verifique os logs regularmente"
echo
echo "🎉 Sistema pronto para uso!"
