import { useMemo } from 'react'
import { SSHServer, Command } from '../types/server'
import ServerCard from './ServerCard'

interface ServerListProps {
  servers: (SSHServer & { commands: Command[] })[]
  onServerUpdated: () => void
}

export default function ServerList({ servers, onServerUpdated }: ServerListProps) {
  const sortedServers = useMemo(() => {
    return [...servers].sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )
  }, [servers])

  if (sortedServers.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg text-gray-500 dark:text-gray-400">
          Nenhum host cadastrado ainda.
        </h3>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 pb-8 items-stretch">
      {sortedServers.map((server) => (
        <div key={server.id} className="relative h-full">
          <ServerCard
            server={server}
            onServerUpdated={onServerUpdated}
          />
        </div>
      ))}
    </div>
  )
} 