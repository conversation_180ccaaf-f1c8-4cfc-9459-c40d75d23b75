import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { Filter, FolderOpen, X } from 'lucide-react'
import { listServerGroups } from '../services/serverGroupApi'
import { ServerGroup } from '../types/serverGroup'

interface ServerGroupFilterProps {
  selectedGroupId: string | null
  onGroupSelect: (groupId: string | null) => void
}

export default function ServerGroupFilter({ selectedGroupId, onGroupSelect }: ServerGroupFilterProps) {
  const { data: groups, isLoading } = useQuery({
    queryKey: ['server-groups'],
    queryFn: listServerGroups,
  })

  const selectedGroup = groups?.find(group => group.id === selectedGroupId)

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
        <Filter className="h-4 w-4 text-gray-400 dark:text-gray-500" />
        <span className="text-sm text-gray-500 dark:text-gray-400">Carregando grupos...</span>
      </div>
    )
  }

  if (!groups || groups.length === 0) {
    return null
  }

  return (
    <div className="relative">
      <div className="flex items-center gap-2">
        {/* Dropdown de grupos */}
        <div className="relative">
          <select
            value={selectedGroupId || ''}
            onChange={(e) => onGroupSelect(e.target.value || null)}
            className="appearance-none bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 pr-8 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 transition-colors"
          >
            <option value="">Todos os hosts</option>
            {groups.map((group) => (
              <option key={group.id} value={group.id}>
                {group.name} ({group._count?.members || 0})
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <Filter className="h-4 w-4 text-gray-400 dark:text-gray-500" />
          </div>
        </div>

        {/* Indicador de filtro ativo */}
        {selectedGroup && (
          <div className="flex items-center gap-2 px-3 py-1 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700 rounded-lg">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: selectedGroup.color || '#3B82F6' }}
            />
            <span className="text-sm text-primary-700 dark:text-primary-300 font-medium">
              {selectedGroup.name}
            </span>
            <button
              onClick={() => onGroupSelect(null)}
              className="text-primary-500 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>

      {/* Informação sobre filtro */}
      {selectedGroup && (
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          Mostrando {selectedGroup._count?.members || 0} servidor(es) do grupo "{selectedGroup.name}"
        </div>
      )}
    </div>
  )
}
