import { NodeSSH } from 'node-ssh';
import { Device } from '../deviceTypes';
import { ICommandExecutor } from './baseExecutor';
import { DatacomExecutor } from './datacomExecutor';
import { DmosExecutor } from './dmosExecutor';
import { GenericExecutor } from './genericExecutor';
import { HuaweiExecutor } from './huaweiExecutor';
import { MikrotikExecutor } from './mikrotikExecutor';
import { NokiaExecutor } from './nokiaExecutor';

/**
 * Cria um executor de comandos com base no tipo de dispositivo
 * @param deviceType Tipo de dispositivo
 * @param ssh Instância SSH
 * @param serverInfo Informações do servidor (necessário para Datacom/Telnet)
 * @returns Executor de comandos
 */
export function makeExecutor(
  deviceType: Device,
  ssh: NodeSSH,
  serverInfo?: { host: string; port?: number; username?: string; password?: string }
): ICommandExecutor {
  switch (deviceType) {
    case Device.NOKIA:
      return new NokiaExecutor(ssh);
    case Device.HUAWEI:
      return new HuaweiExecutor(ssh);
    case Device.MIKROTIK:
      return new MikrotikExecutor(ssh);
    case Device.DMOS:
      return new DmosExecutor(ssh);
    case Device.DATACOM:
      if (!serverInfo) {
        throw new Error('Informações do servidor são necessárias para dispositivos Datacom');
      }
      // Para Datacom, usar Telnet ao invés de SSH
      return new DatacomExecutor(
        serverInfo.host,
        serverInfo.port || 23,
        serverInfo.username,
        serverInfo.password
      ) as any; // Cast necessário pois DatacomExecutor não herda de BaseExecutor
    case Device.GENERIC:
    default:
      return new GenericExecutor(ssh);
  }
}

export * from './baseExecutor';
export * from './nokiaExecutor';
export * from './huaweiExecutor';
export * from './mikrotikExecutor';
export * from './dmosExecutor';
export * from './datacomExecutor';
export * from './genericExecutor';
