import { FastifyInstance } from 'fastify'
import { verifyJWT } from '../middlewares/auth'
import { verifyAdmin } from '../middlewares/admin'
import {
  listServerGroups,
  getServerGroup,
  createServerGroup,
  updateServerGroup,
  deleteServerGroup,
  addServerToGroup,
  removeServerFromGroup,
} from '../controllers/serverGroups'

export async function serverGroupRoutes(app: FastifyInstance) {
  // Middleware de autenticação para todas as rotas
  app.addHook('onRequest', verifyJWT)

  // Listar grupos do usuário
  app.get('/', listServerGroups)

  // Obter grupo específico
  app.get('/:id', getServerGroup)

  // Criar grupo (apenas admin)
  app.post('/', { onRequest: [verifyJWT, verifyAdmin] }, createServerGroup)

  // Atualizar grupo (apenas admin)
  app.put('/:id', { onRequest: [verifyJWT, verifyAdmin] }, updateServerGroup)

  // Excluir grupo (apenas admin)
  app.delete('/:id', { onRequest: [verifyJWT, verifyAdmin] }, deleteServerGroup)

  // Adicionar servidor ao grupo (apenas admin)
  app.post('/:id/servers', { onRequest: [verifyJWT, verifyAdmin] }, addServerToGroup)

  // Remover servidor do grupo (apenas admin)
  app.delete('/:id/servers/:serverId', { onRequest: [verifyJWT, verifyAdmin] }, removeServerFromGroup)
}
