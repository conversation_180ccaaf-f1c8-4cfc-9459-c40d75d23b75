import React, { Fragment, useState, useEffect } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { X, Palette, Plus, Edit, Trash2, Check, Eye, ArrowLeft, Loader2 } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { terminalThemeService } from '../services/terminalThemeService'
import { TerminalTheme, CreateTerminalThemeDTO } from '../types/terminalTheme'
import ConfirmModal from './ConfirmModal'

interface TerminalThemeModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function TerminalThemeModal({ isOpen, onClose }: TerminalThemeModalProps) {
  const { user, updateUserTheme } = useAuth()
  const isAdmin = user?.role === 'ADMIN'
  
  const [themes, setThemes] = useState<TerminalTheme[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedThemeId, setSelectedThemeId] = useState<string | null>(user?.terminalThemeId || null)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingTheme, setEditingTheme] = useState<TerminalTheme | null>(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [themeToDelete, setThemeToDelete] = useState<TerminalTheme | null>(null)
  const [isSelectingTheme, setIsSelectingTheme] = useState(false)
  const [selectingThemeId, setSelectingThemeId] = useState<string | null>(null)

  // Estados do formulário integrado
  const [formData, setFormData] = useState<CreateTerminalThemeDTO>({
    name: '',
    backgroundColor: '#1a1a1a',
    textColor: '#00ff88',
    isDefault: false
  })
  const [formError, setFormError] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (isOpen) {
      loadThemes()
    }
  }, [isOpen])

  // Sincronizar formData com mudanças no editingTheme
  useEffect(() => {
    if (editingTheme) {
      setFormData({
        name: editingTheme.name,
        backgroundColor: editingTheme.backgroundColor,
        textColor: editingTheme.textColor,
        isDefault: editingTheme.isDefault
      })
    } else {
      resetForm()
    }
  }, [editingTheme])

  const resetForm = () => {
    setFormData({
      name: '',
      backgroundColor: '#1a1a1a',
      textColor: '#00ff88',
      isDefault: false
    })
    setFormError('')
  }

  const loadThemes = async () => {
    try {
      setIsLoading(true)
      const themesData = await terminalThemeService.getAll()
      setThemes(themesData)
    } catch (error) {
      console.error('Erro ao carregar temas:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleThemeSelect = async (themeId: string | null) => {
    try {
      setIsSelectingTheme(true)
      setSelectingThemeId(themeId)
      await updateUserTheme(themeId)
      setSelectedThemeId(themeId)
    } catch (error) {
      console.error('Erro ao atualizar tema:', error)
    } finally {
      setIsSelectingTheme(false)
      setSelectingThemeId(null)
    }
  }

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormError('')
    setIsSubmitting(true)

    try {
      let theme: TerminalTheme

      if (editingTheme) {
        theme = await terminalThemeService.update(editingTheme.id, formData)
        setThemes(prev => prev.map(t => t.id === theme.id ? theme : t))
      } else {
        theme = await terminalThemeService.create(formData)
        setThemes(prev => [...prev, theme])
      }

      setShowCreateForm(false)
      setEditingTheme(null)
      resetForm()
    } catch (error: any) {
      setFormError(error.response?.data?.message || 'Erro ao salvar tema')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleFormCancel = () => {
    setShowCreateForm(false)
    setEditingTheme(null)
    resetForm()
  }

  const handleDeleteTheme = async () => {
    if (!themeToDelete) return

    try {
      await terminalThemeService.delete(themeToDelete.id)
      setThemes(prev => prev.filter(theme => theme.id !== themeToDelete.id))

      // Se o tema deletado era o selecionado pelo usuário atual,
      // o backend já atualizou para o tema padrão, então precisamos atualizar o contexto
      if (selectedThemeId === themeToDelete.id) {
        // Buscar o tema padrão
        const defaultTheme = themes.find(theme => theme.isDefault)
        if (defaultTheme) {
          await handleThemeSelect(defaultTheme.id)
        } else {
          await handleThemeSelect(null)
        }
      }
    } catch (error) {
      console.error('Erro ao deletar tema:', error)
      // Aqui você pode adicionar uma notificação de erro se desejar
    } finally {
      setShowDeleteModal(false)
      setThemeToDelete(null)
    }
  }

  // Função para controlar o fechamento do modal principal
  // Só permite fechar se não houver modais secundários abertos
  const handleMainModalClose = () => {
    if (!showCreateForm && !showDeleteModal) {
      onClose()
    }
  }

  const ThemePreview = ({ theme }: { theme: TerminalTheme }) => {
    const isCurrentlySelecting = selectingThemeId === theme.id
    const isSelected = selectedThemeId === theme.id
    const isDisabled = isSelectingTheme && !isCurrentlySelecting

    return (
      <div
        className={`relative border rounded-lg p-3 transition-all duration-300 ease-in-out ${
          isDisabled
            ? 'cursor-not-allowed'
            : 'cursor-pointer hover:scale-105'
        } ${
          isCurrentlySelecting
            ? 'ring-2 ring-blue-400 ring-opacity-75 animate-pulse'
            : ''
        }`}
        style={{
          backgroundColor: theme.backgroundColor,
          borderColor: isSelected ? theme.textColor : '#374151'
        }}
        onClick={() => !isDisabled && handleThemeSelect(theme.id)}
      >
        {isCurrentlySelecting && (
          <div className="absolute top-1 right-1">
            <Loader2 className="h-4 w-4 animate-spin" style={{ color: theme.textColor }} />
          </div>
        )}
        {isSelected && !isCurrentlySelecting && (
          <div className="absolute top-1 right-1">
            <Check className="h-4 w-4" style={{ color: theme.textColor }} />
          </div>
        )}

        <div className="space-y-2">
          <div className="text-xs font-medium" style={{ color: theme.textColor }}>
            {theme.name}
          </div>

          <div className="text-xs opacity-80" style={{ color: theme.textColor }}>
            $ ls -la
          </div>

          <div className="text-xs opacity-60" style={{ color: theme.textColor }}>
            drwxr-xr-x 2 <USER> <GROUP> 4096 Jun 28 12:00 .
          </div>

          <div className="flex justify-between items-center text-xs">
            <span style={{ color: theme.textColor, opacity: 0.5 }}>
              {theme.isDefault && '(Padrão)'}
            </span>

            {isAdmin && (
              <div className="flex gap-1">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    setEditingTheme(theme)
                    setShowCreateForm(true)
                  }}
                  className="p-1 rounded hover:bg-black hover:bg-opacity-20"
                  title="Editar tema"
                  disabled={isDisabled}
                >
                  <Edit className="h-3 w-3" style={{ color: theme.textColor }} />
                </button>

                {!theme.isDefault && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      setThemeToDelete(theme)
                      setShowDeleteModal(true)
                    }}
                    className="p-1 rounded hover:bg-black hover:bg-opacity-20"
                    title="Excluir tema"
                    disabled={isDisabled}
                  >
                    <Trash2 className="h-3 w-3" style={{ color: theme.textColor }} />
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleMainModalClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div
            className={`fixed inset-0 bg-black bg-opacity-25 ${
              showCreateForm || showDeleteModal ? 'pointer-events-none' : ''
            }`}
          />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-2">
                    {showCreateForm && (
                      <button
                        onClick={handleFormCancel}
                        className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 mr-2"
                      >
                        <ArrowLeft className="h-5 w-5" />
                      </button>
                    )}
                    <Palette className="h-6 w-6 text-blue-600" />
                    <Dialog.Title as="h3" className="text-lg font-medium text-gray-900 dark:text-gray-100">
                      {showCreateForm
                        ? (editingTheme ? 'Editar Tema' : 'Criar Novo Tema')
                        : 'Temas do Terminal'
                      }
                    </Dialog.Title>
                  </div>

                  <div className="flex items-center gap-2">
                    {!showCreateForm && isAdmin && (
                      <button
                        onClick={() => {
                          setEditingTheme(null)
                          setShowCreateForm(true)
                        }}
                        className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <Plus className="h-4 w-4" />
                        Novo Tema
                      </button>
                    )}

                    <button
                      onClick={showCreateForm ? handleFormCancel : onClose}
                      className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                {showCreateForm ? (
                  <form onSubmit={handleFormSubmit} className="space-y-6">
                    {formError && (
                      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg">
                        {formError}
                      </div>
                    )}

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                        Nome do Tema
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Ex: Meu Tema Personalizado"
                        required
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                          Cor de Fundo
                        </label>
                        <div className="flex gap-2">
                          <input
                            type="color"
                            value={formData.backgroundColor}
                            onChange={(e) => setFormData({ ...formData, backgroundColor: e.target.value })}
                            className="w-12 h-10 border border-gray-300 dark:border-gray-600 rounded cursor-pointer"
                          />
                          <input
                            type="text"
                            value={formData.backgroundColor}
                            onChange={(e) => setFormData({ ...formData, backgroundColor: e.target.value })}
                            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="#1a1a1a"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                          Cor do Texto
                        </label>
                        <div className="flex gap-2">
                          <input
                            type="color"
                            value={formData.textColor}
                            onChange={(e) => setFormData({ ...formData, textColor: e.target.value })}
                            className="w-12 h-10 border border-gray-300 dark:border-gray-600 rounded cursor-pointer"
                          />
                          <input
                            type="text"
                            value={formData.textColor}
                            onChange={(e) => setFormData({ ...formData, textColor: e.target.value })}
                            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="#00ff88"
                          />
                        </div>
                      </div>
                    </div>

                    {isAdmin && (
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="isDefault"
                          checked={formData.isDefault}
                          onChange={(e) => setFormData({ ...formData, isDefault: e.target.checked })}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                        />
                        <label htmlFor="isDefault" className="ml-2 block text-sm text-gray-700 dark:text-gray-200">
                          Definir como tema padrão
                        </label>
                      </div>
                    )}

                    {/* Preview */}
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Eye className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-200">Preview</span>
                      </div>

                      <div
                        className="border rounded-lg p-4 font-mono text-sm"
                        style={{
                          backgroundColor: formData.backgroundColor,
                          color: formData.textColor
                        }}
                      >
                        <div className="space-y-1">
                          <div>$ ls -la</div>
                          <div className="opacity-80">drwxr-xr-x 2 <USER> <GROUP> 4096 Jun 28 12:00 .</div>
                          <div className="opacity-80">drwxr-xr-x 3 <USER> <GROUP> 4096 Jun 28 11:30 ..</div>
                          <div className="opacity-80">-rw-r--r-- 1 <USER> <GROUP>  220 Jun 28 12:00 .bashrc</div>
                          <div className="opacity-60">$ _</div>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end gap-3">
                      <button
                        type="button"
                        onClick={handleFormCancel}
                        className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-2 focus:ring-blue-500"
                        disabled={isSubmitting}
                      >
                        Cancelar
                      </button>
                      <button
                        type="submit"
                        className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? 'Salvando...' : (editingTheme ? 'Atualizar' : 'Criar Tema')}
                      </button>
                    </div>
                  </form>
                ) : isLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        Selecione um tema para personalizar a aparência do seu terminal
                      </p>

                      <button
                        onClick={() => handleThemeSelect(null)}
                        disabled={isSelectingTheme}
                        className={`flex items-center gap-2 px-3 py-1 text-sm rounded-lg transition-all duration-300 ease-in-out ${
                          selectedThemeId === null
                            ? 'bg-gray-800 dark:bg-gray-600 text-white'
                            : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
                        } ${isSelectingTheme && selectingThemeId === null ? 'ring-2 ring-blue-400 ring-opacity-75 animate-pulse' : ''}`}
                      >
                        {isSelectingTheme && selectingThemeId === null && (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        )}
                        Tema Padrão
                      </button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {themes.map((theme) => (
                        <ThemePreview key={theme.id} theme={theme} />
                      ))}
                    </div>
                  </div>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>



    {/* Modal de Confirmação de Exclusão */}
    <ConfirmModal
      isOpen={showDeleteModal}
      onClose={() => {
        setShowDeleteModal(false)
        setThemeToDelete(null)
      }}
      onConfirm={handleDeleteTheme}
      title="Excluir Tema"
      message={`Tem certeza que deseja excluir o tema "${themeToDelete?.name}"? Usuários que estão usando este tema serão automaticamente redirecionados para o tema padrão. Esta ação não pode ser desfeita.`}
      confirmText="Excluir"
      confirmStyle="danger"
    />
  </>
  )
}
