import React, { useState } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { listCommandTemplates, deleteCommandTemplate } from '../services/api'
import { CommandTemplate, Command } from '../types/server'
import { BookTemplate, Plus, Trash2, Edit, Copy, User, Download } from 'lucide-react'
import TemplateModal from '../components/TemplateModal'
import ConfirmModal from '../components/ConfirmModal'
import ApplyTemplateModal from '../components/ApplyTemplateModal'
import CopyFromServerModal from '../components/CopyFromServerModal'
import { useTheme } from '../contexts/ThemeContext'

export function CommandTemplates() {
  const queryClient = useQueryClient()
  const { theme } = useTheme()
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [isApplyModalOpen, setIsApplyModalOpen] = useState(false)
  const [isCopyFromServerModalOpen, setIsCopyFromServerModalOpen] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<CommandTemplate | null>(null)
  const [copiedCommands, setCopiedCommands] = useState<Omit<Command, 'id' | 'serverId' | 'createdAt' | 'updatedAt'>[]>([])
  const [isCreateWithCopiedCommands, setIsCreateWithCopiedCommands] = useState(false)

  // Determinar o tema atual
  const actualTheme = theme === 'system'
    ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
    : theme

  const { data: templates = [], isLoading, error } = useQuery({
    queryKey: ['commandTemplates'],
    queryFn: listCommandTemplates,
  })

  function handleCreateTemplate() {
    setIsCreateWithCopiedCommands(false)
    setCopiedCommands([])
    setIsCreateModalOpen(true)
  }

  function handleCopyFromServer() {
    setIsCopyFromServerModalOpen(true)
  }

  function handleCommandsSelected(commands: Omit<Command, 'id' | 'serverId' | 'createdAt' | 'updatedAt'>[]) {
    setCopiedCommands(commands)
    setIsCreateWithCopiedCommands(true)
    setIsCreateModalOpen(true)
  }

  function handleEditTemplate(template: CommandTemplate) {
    setSelectedTemplate(template)
    setIsEditModalOpen(true)
  }

  function handleDeleteTemplate(template: CommandTemplate) {
    setSelectedTemplate(template)
    setIsDeleteModalOpen(true)
  }

  function handleApplyTemplate(template: CommandTemplate) {
    setSelectedTemplate(template)
    setIsApplyModalOpen(true)
  }

  async function confirmDeleteTemplate() {
    if (!selectedTemplate) return

    try {
      await deleteCommandTemplate(selectedTemplate.id)
      queryClient.invalidateQueries({ queryKey: ['commandTemplates'] })
      setIsDeleteModalOpen(false)
    } catch (error) {
      console.error('Erro ao excluir template:', error)
    }
  }

  return (
    <div className="container mx-auto px-4 py-6 sm:py-8">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <h1 className={`text-xl sm:text-2xl font-bold ${actualTheme === 'dark' ? 'text-white' : 'text-gray-800'}`}>Templates de Comandos</h1>
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <button
            onClick={handleCopyFromServer}
            className="inline-flex items-center justify-center gap-2 bg-orange-600 text-white hover:bg-orange-700 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 w-full sm:w-auto"
          >
            <Download className="h-5 w-5" />
            Copiar de Servidor
          </button>
          <button
            onClick={handleCreateTemplate}
            className="inline-flex items-center justify-center gap-2 bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 w-full sm:w-auto"
          >
            <Plus className="h-5 w-5" />
            Novo Template
          </button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          Erro ao carregar templates. Por favor, tente novamente.
        </div>
      ) : templates.length === 0 ? (
        <div className={`border px-4 py-8 rounded-md text-center ${
          actualTheme === 'dark'
            ? 'bg-gray-800 border-gray-600 text-gray-300'
            : 'bg-gray-100 border-gray-300 text-gray-700'
        }`}>
          <p className="text-lg mb-4">Nenhum template de comandos encontrado</p>
          <p className={`text-sm mb-4 ${
            actualTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
          }`}>
            Crie templates para reutilizar conjuntos de comandos em diferentes servidores.
          </p>
          <div className="flex flex-col sm:flex-row gap-2 justify-center">
            <button
              onClick={handleCopyFromServer}
              className="inline-flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <Download className="h-5 w-5" />
              Copiar de Servidor
            </button>
            <button
              onClick={handleCreateTemplate}
              className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus className="h-5 w-5" />
              Criar Primeiro Template
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {templates.map((template) => (
            <div
              key={template.id}
              className={`border rounded-lg shadow-sm overflow-hidden ${
                actualTheme === 'dark'
                  ? 'bg-gray-800 border-gray-600'
                  : 'bg-white border-gray-200'
              }`}
            >
              <div className="p-4 sm:p-5">
                <div className="flex justify-between items-start mb-2">
                  <h3 className={`text-lg sm:text-xl font-semibold truncate flex items-center gap-2 ${
                    actualTheme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`} title={template.name}>
                    <BookTemplate className="h-5 w-5 text-blue-500 flex-shrink-0" />
                    <span className="truncate">{template.name}</span>
                  </h3>
                </div>

                {template.description && (
                  <p className={`text-sm mb-4 line-clamp-2 ${
                    actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
                  }`} title={template.description}>{template.description}</p>
                )}

                <div className="mb-4">
                  <p className={`text-sm mb-1 ${
                    actualTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                  }`}>
                    {template.commands.length} comando{template.commands.length !== 1 ? 's' : ''}
                  </p>
                  <div className={`text-xs flex items-center gap-1 ${
                    actualTheme === 'dark' ? 'text-gray-500' : 'text-gray-400'
                  }`}>
                    <User className="h-3 w-3 flex-shrink-0" />
                    <span className="truncate">Criado por: {template.user?.name || 'Você'}</span>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <button
                    onClick={() => handleApplyTemplate(template)}
                    className={`inline-flex items-center gap-1 text-sm px-2 py-1 rounded ${
                      actualTheme === 'dark'
                        ? 'text-blue-400 hover:text-blue-300 hover:bg-blue-900/50'
                        : 'text-blue-600 hover:text-blue-800 hover:bg-blue-50'
                    }`}
                    title="Aplicar a um servidor"
                  >
                    <Copy className="h-4 w-4 flex-shrink-0" />
                    Aplicar
                  </button>

                  <div className="flex gap-2">
                    <button
                      onClick={() => handleEditTemplate(template)}
                      className={`p-1 rounded-full ${
                        actualTheme === 'dark'
                          ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                      }`}
                      title="Editar template"
                    >
                      <Edit className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleDeleteTemplate(template)}
                      className={`p-1 text-red-500 hover:text-red-700 rounded-full ${
                        actualTheme === 'dark' ? 'hover:bg-red-900' : 'hover:bg-red-100'
                      }`}
                      title="Excluir template"
                    >
                      <Trash2 className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modal de Criação */}
      {isCreateModalOpen && (
        <TemplateModal
          isOpen={isCreateModalOpen}
          onClose={() => {
            setIsCreateModalOpen(false)
            setIsCreateWithCopiedCommands(false)
            setCopiedCommands([])
          }}
          mode="create"
          initialCommands={isCreateWithCopiedCommands ? copiedCommands : undefined}
        />
      )}

      {/* Modal de Edição */}
      {isEditModalOpen && selectedTemplate && (
        <TemplateModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          mode="edit"
          template={selectedTemplate}
        />
      )}

      {/* Modal de Confirmação de Exclusão */}
      {isDeleteModalOpen && selectedTemplate && (
        <ConfirmModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          onConfirm={confirmDeleteTemplate}
          title="Excluir Template"
          message={`Tem certeza que deseja excluir o template "${selectedTemplate.name}"? Esta ação não pode ser desfeita.`}
          confirmText="Excluir"
          confirmStyle="danger"
        />
      )}

      {/* Modal de Aplicação de Template */}
      {isApplyModalOpen && selectedTemplate && (
        <ApplyTemplateModal
          isOpen={isApplyModalOpen}
          onClose={() => setIsApplyModalOpen(false)}
          template={selectedTemplate}
          onServerUpdated={() => {
            // Invalidar queries de servidores para garantir dados atualizados
            queryClient.invalidateQueries({ queryKey: ['servers'] })
          }}
        />
      )}

      {/* Modal de Cópia de Servidor */}
      {isCopyFromServerModalOpen && (
        <CopyFromServerModal
          isOpen={isCopyFromServerModalOpen}
          onClose={() => setIsCopyFromServerModalOpen(false)}
          onCommandsSelected={handleCommandsSelected}
        />
      )}
    </div>
  )
}