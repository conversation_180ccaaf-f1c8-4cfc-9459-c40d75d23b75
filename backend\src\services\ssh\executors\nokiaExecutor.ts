import { NodeSSH } from 'node-ssh';
import { CommandResult } from '../../../types/server';
import { Logger } from '../../../utils/logger';
import { BaseExecutor } from './baseExecutor';
import { calculateMaxCompletionChecks } from '../../../config/loopProtection';

/**
 * Executor de comandos para dispositivos Nokia
 */
export class NokiaExecutor extends BaseExecutor {
  constructor(ssh: NodeSSH) {
    super(ssh);
  }

  /**
   * Executa um comando em um dispositivo Nokia
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  async executeCommand(command: string): Promise<CommandResult> {
    try {
      // Verificar se o comando contém múltiplas linhas
      if (command.includes('\n')) {
        Logger.log('Detectado comando com múltiplas linhas em dispositivo Nokia, usando modo especial');
        return await this.executeNokiaMultilineCommand(command);
      }

      // Limpar o comando para remover caracteres invisíveis e espaços extras
      const cleanCommand = command.trim().replace(/\s+/g, ' ');

      Logger.log(`Executando comando Nokia: ${cleanCommand.substring(0, 50)}${cleanCommand.length > 50 ? '...' : ''}`);

      // Para dispositivos Nokia, sempre usar shell interativo
      // Estimar a complexidade do comando para o timeout dinâmico
      const commandComplexity = Math.max(2, cleanCommand.split(';').length + cleanCommand.split('|').length);

      return await this.executeInteractiveCommand(cleanCommand, {
        term: 'vt100',
        rows: 24,
        cols: 80,
        inactivityTimeout: 2000, // Aumentado para 2000ms para garantir captura completa da saída
        commandCount: commandComplexity // Usar a complexidade do comando para o timeout dinâmico
      });
    } catch (error) {
      Logger.error('Erro ao executar comando Nokia:', error);
      throw new Error(`Falha ao executar comando Nokia: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Executa um comando com múltiplas linhas em um dispositivo Nokia
   * @param command Comando com múltiplas linhas
   * @returns Resultado do comando
   */
  async executeNokiaMultilineCommand(command: string): Promise<CommandResult> {
    Logger.log('Executando comando multilinhas em modo especial para Nokia');

    // Para Nokia, precisamos enviar todos os comandos em uma única sessão
    // para manter o contexto de configuração
    const cleanCommand = command.trim();

    // Verificar se o comando começa com 'configure global'
    if (!cleanCommand.startsWith('configure global')) {
      Logger.log('Adicionando "configure global" automaticamente ao comando Nokia');
      // Adicionar 'configure global' no início se não estiver presente
      return await this.executeInteractiveNokiaCommand('configure global\n' + cleanCommand);
    }

    Logger.log('Usando modo especial para comandos multilinhas em Nokia');

    // Usar o método interativo para enviar todos os comandos de uma vez
    return await this.executeInteractiveNokiaCommand(cleanCommand);
  }

  /**
   * Executa um comando interativo em um dispositivo Nokia
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  private executeInteractiveNokiaCommand(command: string): Promise<CommandResult> {
    return new Promise((resolve, reject) => {
      let shell: any = null;
      let commandTimeout: NodeJS.Timeout | null = null;
      let globalTimeout: NodeJS.Timeout | null = null;

      // Calcular timeout dinâmico com base na quantidade de comandos
      const lines = command.split('\n');
      const commandCount = lines.length;
      const MAX_EXECUTION_TIME = this.calculateDynamicTimeout(commandCount);
      // Aumentado para 1500ms para garantir captura completa da saída
      const INACTIVITY_TIMEOUT = 1500;

      // Proteção contra loops infinitos - limite absoluto de verificações
      let completionCheckCount = 0;
      const MAX_COMPLETION_CHECKS = calculateMaxCompletionChecks(commandCount);

      Logger.log(`Usando timeout dinâmico de ${MAX_EXECUTION_TIME}ms para ${commandCount} comandos Nokia`)

      try {
        let output = '';
        let errorOutput = '';
        let lastDataTime = Date.now();
        let commandCompleted = false;

        // Função para limpar recursos com tratamento de erros aprimorado
        const cleanup = () => {
          // Limpar timers primeiro
          if (commandTimeout) {
            try {
              clearTimeout(commandTimeout);
            } catch (e) {
              Logger.error('Erro ao limpar commandTimeout:', e);
            }
            commandTimeout = null;
          }

          if (globalTimeout) {
            try {
              clearTimeout(globalTimeout);
            } catch (e) {
              Logger.error('Erro ao limpar globalTimeout:', e);
            }
            globalTimeout = null;
          }

          // Limpar shell com tratamento de erros robusto
          if (shell) {
            try {
              // Remover todos os listeners para evitar vazamentos de memória
              try {
                shell.removeAllListeners('data');
                shell.removeAllListeners('error');
                shell.removeAllListeners('close');
                shell.removeAllListeners('end');
              } catch (listenerError) {
                Logger.error('Erro ao remover listeners do shell Nokia:', listenerError);
                // Continuar mesmo com erro nos listeners
              }

              // Tentar encerrar o shell se ainda não estiver encerrado
              if (!shell.ended) {
                try {
                  shell.end();
                } catch (endError) {
                  Logger.error('Erro ao encerrar shell Nokia:', endError);
                  // Não propagar o erro, apenas registrar
                }
              }
            } catch (shellError) {
              Logger.error('Erro crítico ao limpar shell Nokia:', shellError);
            } finally {
              // Garantir que a referência ao shell seja limpa
              shell = null;
            }
          }
        };

        // Timeout global para garantir que o comando não execute indefinidamente
        globalTimeout = setTimeout(() => {
          Logger.warn(`Timeout global atingido após ${MAX_EXECUTION_TIME}ms para o comando Nokia: ${command}`);
          cleanup();
          resolve({
            stdout: output,
            stderr: errorOutput + '\n[ERRO] Timeout global atingido após 120 segundos',
            code: 124 // Código de timeout
          });
        }, MAX_EXECUTION_TIME);

        // Variáveis para verificar estabilidade da saída
        let lastOutputLength = 0;
        let stableOutputCount = 0;
        const STABILITY_THRESHOLD = 2; // Número de verificações consecutivas sem mudanças para considerar estável

        // Função aprimorada para verificar se o comando foi concluído por inatividade ou estabilidade da saída
        const checkCompletion = () => {
          // Proteção contra loops infinitos - verificar limite de tentativas
          completionCheckCount++;
          if (completionCheckCount > MAX_COMPLETION_CHECKS) {
            Logger.warn(`Limite de verificações de conclusão atingido (${MAX_COMPLETION_CHECKS}), forçando finalização`);
            commandCompleted = true;
            cleanup();

            // Se há saída substancial (mais de 500 caracteres), não mostrar aviso
            const hasSubstantialOutput = output.length > 500;
            const warningMessage = hasSubstantialOutput ? '' : '\n[AVISO] Comando finalizado por limite de verificações de segurança.';

            resolve({
              stdout: output,
              stderr: errorOutput + warningMessage,
              code: 0
            });
            return;
          }

          const now = Date.now();
          const timeSinceLastData = now - lastDataTime;
          const currentOutputLength = output.length;

          // Verificar se a saída está estável (não mudou desde a última verificação)
          if (currentOutputLength === lastOutputLength) {
            stableOutputCount++;
            Logger.log(`Saída estável por ${stableOutputCount} verificações consecutivas (${output.length} bytes) - Check ${completionCheckCount}/${MAX_COMPLETION_CHECKS}`);
          } else {
            stableOutputCount = 0;
            lastOutputLength = currentOutputLength;
          }

          // Condições de conclusão:
          // 1. Inatividade por tempo suficiente
          // 2. Saída estável por várias verificações consecutivas e pelo menos algum tempo de inatividade
          if ((timeSinceLastData >= INACTIVITY_TIMEOUT && !commandCompleted) ||
              (stableOutputCount >= STABILITY_THRESHOLD && timeSinceLastData >= (INACTIVITY_TIMEOUT / 2) && !commandCompleted)) {

            if (timeSinceLastData >= INACTIVITY_TIMEOUT) {
              Logger.log(`Comando Nokia concluído após ${timeSinceLastData}ms de inatividade`);
            } else {
              Logger.log(`Comando Nokia concluído por estabilidade da saída após ${stableOutputCount} verificações`);
            }

            // Verificar se a saída termina com um prompt conhecido antes de finalizar
            const lastLines = output.split('\n').slice(-5).join('\n');
            const hasPrompt = /(\[gl:\/configure\]|A:[\w\-]+[@#]|#\s*$|>\s*$|\*A:[\w\-]+[@#]|A:[\w\-]+\$|INFO:\s*CLI\s*#)/i.test(lastLines);

            if (hasPrompt || timeSinceLastData >= INACTIVITY_TIMEOUT * 1.5) {
              commandCompleted = true;
              cleanup();
              resolve({
                stdout: output,
                stderr: errorOutput,
                code: 0
              });
            } else {
              // Se não terminar com prompt conhecido, aguardar um pouco mais
              Logger.log('Saída não termina com prompt conhecido, aguardando mais dados...');
              lastDataTime = Date.now(); // Resetar o tempo para dar mais uma chance

              // Verificar se ainda não atingiu o limite de verificações
              if (completionCheckCount < MAX_COMPLETION_CHECKS) {
                commandTimeout = setTimeout(checkCompletion, 500);
              } else {
                Logger.warn('Limite de verificações atingido, finalizando comando');
                commandCompleted = true;
                cleanup();

                // Se há saída substancial, não mostrar aviso
                const hasSubstantialOutput = output.length > 500;
                const warningMessage = hasSubstantialOutput ? '' : '\n[AVISO] Comando finalizado por limite de verificações.';

                resolve({
                  stdout: output,
                  stderr: errorOutput + warningMessage,
                  code: 0
                });
              }
            }
          } else if (!commandCompleted && completionCheckCount < MAX_COMPLETION_CHECKS) {
            // Verificar a cada 500ms apenas se não atingiu o limite
            commandTimeout = setTimeout(checkCompletion, 500);
          } else if (completionCheckCount >= MAX_COMPLETION_CHECKS) {
            Logger.warn('Limite de verificações atingido durante espera, finalizando comando');
            commandCompleted = true;
            cleanup();

            // Se há saída substancial, não mostrar aviso
            const hasSubstantialOutput = output.length > 500;
            const warningMessage = hasSubstantialOutput ? '' : '\n[AVISO] Comando finalizado por limite de verificações de segurança.';

            resolve({
              stdout: output,
              stderr: errorOutput + warningMessage,
              code: 0
            });
          }
        };

        // Função aprimorada para verificar padrões em uma única passagem
        const checkPatterns = (data: string) => {
          // Padrões de paginação - detecta quando o dispositivo está esperando por input para mostrar mais conteúdo
          if (/(--\s*More\s*--)|(press\s+Enter)|(load\s+more)|(--\(more\).+--)|(<space>)|(More:)|(\[More\])|(^\s*-{3,}\s*$)|(^\s*:{3,}\s*$)|(Press any key to continue)|(Press <SPACE> to continue)/i.test(data)) {
            Logger.log('Prompt de paginação detectado, enviando Espaço');
            shell.write(' ');
            lastDataTime = Date.now();
          }
          // Padrões de erro de parâmetros
          else if (/(\^\s*Error: Too many parameters found at)|(Unable to exec)|(Invalid parameter)|(Unknown command)|(Syntax error)|(Command not found)/i.test(data)) {
            Logger.log('Erro de parâmetros ou execução detectado, enviando Enter');
            try {
              shell.write('\n');
            } catch (writeError) {
              Logger.error('Erro ao enviar Enter após erro de parâmetros:', writeError);
              // Não propagar o erro, apenas registrar
            }
            lastDataTime = Date.now();

            // Finalizar após erro de parâmetros, mas com tempo maior para capturar mensagens de erro
            setTimeout(() => {
              if (!commandCompleted) {
                Logger.log('Comando concluído após erro de parâmetros ou execução');
                commandCompleted = true;
                cleanup();
                resolve({
                  stdout: output,
                  stderr: errorOutput + '\n[AVISO] Comando pode ter sido executado parcialmente devido a erro de parâmetros ou execução.',
                  code: 0
                });
              }
            }, 1000); // Aumentado para 2000ms para capturar mensagens de erro completas
          }
          // Padrões de prompt de configuração
          else if (/(\[gl:\/configure\])|(INFO: CLI #\d+: Entering global configuration mode)|(A:[\w\-]+[@#])/i.test(data)) {
            Logger.log('Prompt de configuração Nokia detectado');
            lastDataTime = Date.now();
          }

          // Verificações para padrões comuns que indicam atividade
          if (data.includes('[gl:/configure]') ||
              data.includes('A:edgedata@') ||
              data.includes('INFO: CLI #') ||
              data.includes('Entering global configuration mode') ||
              data.includes('*A:') ||
              data.includes('A:') ||
              data.includes('# ') ||
              data.includes('> ') ||
              data.includes('Processing...') ||
              data.includes('Please wait...')) {
            lastDataTime = Date.now();
          }
          // Padrões de conclusão de commit
          else if (data.includes('Commit completed') ||
                  (data.includes('commit') && data.includes('completed')) ||
                  data.includes('Configuration committed successfully')) {
            Logger.log('Commit concluído detectado');
            // Aumentar o tempo de espera após commit para garantir captura completa
            setTimeout(() => {
              if (!commandCompleted) {
                commandCompleted = true;
                cleanup();
                resolve({
                  stdout: output,
                  stderr: errorOutput,
                  code: 0
                });
              }
            }, 1500); // Aumentado para 3000ms para garantir captura completa após commit
          }
          // Padrões de erro de canal
          else if (data.includes('Channel open failure') ||
                  data.includes('open failed') ||
                  data.includes('Connection closed') ||
                  data.includes('Connection reset')) {
            Logger.log('Erro de canal SSH detectado');
            setTimeout(() => {
              if (!commandCompleted) {
                commandCompleted = true;
                cleanup();
                resolve({
                  stdout: output,
                  stderr: errorOutput + '\n[ERRO] Falha no canal SSH. Tente reconectar.',
                  code: 1
                });
              }
            }, 500); // Aumentado para 1000ms
          }

          // Detectar padrões de saída extensa (muitas linhas em uma única resposta)
          if (data.length > 1000 || data.split('\n').length > 10) {
            Logger.log('Detectada saída extensa, atualizando lastDataTime');
            lastDataTime = Date.now();
          }
        };

        // Iniciar shell interativo
        this.ssh.requestShell({
          term: 'vt100',
          rows: 24,
          cols: 80,
          wrap: 80,
          ptyType: 'vanilla'
        }).then(shellInstance => {
          shell = shellInstance;

          // Buffer para acumular dados parciais
          let dataBuffer = '';

          shell.on('data', (data: Buffer) => {
            const chunk = data.toString('utf8');

            // Acumular dados no buffer
            dataBuffer += chunk;
            output += chunk;
            lastDataTime = Date.now();

            // Registrar dados recebidos
            Logger.log(`stdout Nokia (${chunk.length} bytes): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);

            // Processar o buffer completo para detectar padrões
            checkPatterns(chunk);

            // Se o buffer contiver linhas completas, processá-las
            if (dataBuffer.includes('\n')) {
              // Dividir o buffer em linhas
              const lines = dataBuffer.split('\n');

              // Manter a última linha (possivelmente incompleta) no buffer
              dataBuffer = lines.pop() || '';

              // Processar cada linha completa
              for (const line of lines) {
                // Atualizar o tempo do último dado para cada linha processada
                lastDataTime = Date.now();

                // Verificar se a linha contém um prompt conhecido
                if (/(\[gl:\/configure\]|A:[\w\-]+[@#]|#\s*$|>\s*$)/i.test(line)) {
                  Logger.log('Prompt detectado na linha: ' + line);
                }
              }
            }
          });

          shell.stderr?.on('data', (data: Buffer) => {
            const chunk = data.toString('utf8');
            errorOutput += chunk;
            lastDataTime = Date.now();
            Logger.error(`stderr Nokia (${chunk.length} bytes): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);
          });

          shell.on('error', (err: Error) => {
            Logger.error('Shell error Nokia:', err);
            errorOutput += `\n[ERRO] ${err.message}`;
            cleanup();
            reject(err);
          });

          shell.on('close', () => {
            Logger.log('Shell Nokia fechado');
            if (!commandCompleted) {
              commandCompleted = true;
              cleanup();
              resolve({
                stdout: output,
                stderr: errorOutput,
                code: 0
              });
            }
          });

          // Aumentado o tempo de inicialização do shell para 1000ms para garantir inicialização completa
          setTimeout(() => {
            Logger.log('Enviando comando multilinhas para Nokia');

            // Estratégia aprimorada: enviar comandos com intervalos adequados
            const sendCommands = () => {
              // Enviar a primeira linha imediatamente
              shell.write(lines[0] + '\n');
              Logger.log(`Enviando linha 1/${lines.length} para Nokia: ${lines[0]}`);

              // Reduzir o tamanho do bloco e aumentar o intervalo entre blocos
              const BLOCK_SIZE = 2; // Reduzido de 3 para 2 comandos por bloco
              const DELAY_BETWEEN_BLOCKS = 500; // Aumentado para 1500ms entre blocos

              // Calcular um atraso adicional para comandos complexos
              const isComplexCommand = (cmd: string): boolean => {
                return cmd.includes('router') ||
                       cmd.includes('bgp') ||
                       cmd.includes('interface') ||
                       cmd.includes('service') ||
                       cmd.includes('system') ||
                       cmd.includes('configure');
              };

              for (let blockStart = 1; blockStart < lines.length; blockStart += BLOCK_SIZE) {
                const blockEnd = Math.min(blockStart + BLOCK_SIZE, lines.length);

                // Verificar se o bloco contém comandos complexos
                let hasComplexCommands = false;
                for (let i = blockStart; i < blockEnd; i++) {
                  if (isComplexCommand(lines[i])) {
                    hasComplexCommands = true;
                    break;
                  }
                }

                // Ajustar o atraso com base na complexidade
                const blockDelay = hasComplexCommands
                  ? DELAY_BETWEEN_BLOCKS * 1.2 // 50% mais tempo para comandos complexos
                  : DELAY_BETWEEN_BLOCKS;

                setTimeout(() => {
                  if (commandCompleted || !shell) return;

                  // Enviar todas as linhas do bloco com pequeno intervalo entre elas
                  for (let i = blockStart; i < blockEnd; i++) {
                    const line = lines[i].trim();
                    if (line) {
                      // Adicionar pequeno atraso entre comandos do mesmo bloco
                      setTimeout(() => {
                        if (commandCompleted || !shell) return;

                        Logger.log(`Enviando linha ${i+1}/${lines.length} para Nokia: ${line}`);
                        shell.write(line + '\n');

                        // Tratamento especial para comando commit
                        if (i === lines.length - 1 && line === 'commit') {
                          Logger.log('Detectado comando commit como última linha');
                          lastDataTime = Date.now();
                        }
                      }, (i - blockStart) * 150); // 300ms entre comandos do mesmo bloco
                    }
                  }

                  // Atualizar o tempo do último dado para evitar timeout entre blocos
                  lastDataTime = Date.now();
                }, (blockStart / BLOCK_SIZE) * blockDelay);
              }
            };

            // Iniciar o envio de comandos
            sendCommands();

            // Iniciar verificação de conclusão com intervalo menor
            commandTimeout = setTimeout(checkCompletion, INACTIVITY_TIMEOUT);
          }, 1000);
        }).catch(error => {
          Logger.error('Erro ao criar shell interativo para Nokia:', error);
          cleanup();
          reject(error);
        });
      } catch (error) {
        Logger.error('Erro no shell interativo para Nokia:', error);
        // Limpar recursos em caso de erro com tratamento robusto
        // Limpar timers primeiro
        if (commandTimeout) {
          try {
            clearTimeout(commandTimeout);
          } catch (e) {
            Logger.error('Erro ao limpar commandTimeout em caso de erro:', e);
          }
          commandTimeout = null;
        }

        if (globalTimeout) {
          try {
            clearTimeout(globalTimeout);
          } catch (e) {
            Logger.error('Erro ao limpar globalTimeout em caso de erro:', e);
          }
          globalTimeout = null;
        }

        // Limpar shell com tratamento de erros robusto
        if (shell) {
          try {
            // Remover todos os listeners para evitar vazamentos de memória
            try {
              shell.removeAllListeners();
            } catch (listenerError) {
              Logger.error('Erro ao remover listeners do shell Nokia em caso de erro:', listenerError);
              // Continuar mesmo com erro nos listeners
            }

            // Tentar encerrar o shell se ainda não estiver encerrado
            if (!shell.ended) {
              try {
                shell.end();
              } catch (endError) {
                Logger.error('Erro ao encerrar shell Nokia em caso de erro:', endError);
                // Não propagar o erro, apenas registrar
              }
            }
          } catch (shellError) {
            Logger.error('Erro crítico ao limpar shell Nokia em caso de erro:', shellError);
          } finally {
            // Garantir que a referência ao shell seja limpa
            shell = null;
          }
        }
        reject(error);
      }
    });
  }
}
