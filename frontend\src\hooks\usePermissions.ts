import { useAuth } from '../contexts/AuthContext'

/**
 * Hook personalizado para verificar permissões do usuário
 */
export function usePermissions() {
  const { user } = useAuth()

  const isAdmin = user?.role === 'ADMIN'
  const isUser = user?.role === 'USER'

  return {
    isAdmin,
    isUser,
    canManageServers: isAdmin,
    canManageGroups: isAdmin,
    canBackupServers: isAdmin,
    canImportServers: isAdmin,
    canManageUsers: isAdmin,
  }
}
