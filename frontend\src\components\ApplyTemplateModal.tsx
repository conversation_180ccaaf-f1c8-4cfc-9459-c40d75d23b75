import React, { Fragment, useState } from 'react'
import { Dialog, Transition, Listbox } from '@headlessui/react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { CommandTemplate, SSHServer } from '../types/server'
import { listServers, applyTemplateToServer } from '../services/api'
import { BookTemplate, Server, Check, X, AlertTriangle, ChevronDown } from 'lucide-react'
import { useTheme } from '../contexts/ThemeContext'

interface ApplyTemplateModalProps {
  isOpen: boolean
  onClose: () => void
  template: CommandTemplate
  onServerUpdated?: () => void
}

export default function ApplyTemplateModal({ isOpen, onClose, template, onServerUpdated }: ApplyTemplateModalProps) {
  const queryClient = useQueryClient()
  const { theme } = useTheme()
  const [selectedServer, setSelectedServer] = useState<SSHServer | null>(null)
  const [isApplying, setIsApplying] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null)

  // Determinar o tema atual
  const actualTheme = theme === 'system'
    ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
    : theme

  const { data: servers = [], isLoading } = useQuery({
    queryKey: ['servers'],
    queryFn: listServers,
  })

  async function handleApplyTemplate() {
    if (!selectedServer) return

    try {
      setIsApplying(true)
      setResult(null)

      const response = await applyTemplateToServer(template.id, selectedServer.id)

      // Invalidar queries para forçar reload dos dados dos servidores
      queryClient.invalidateQueries({ queryKey: ['servers'] })

      // Notificar componente pai sobre a atualização
      if (onServerUpdated) {
        onServerUpdated()
      }

      setResult({
        success: true,
        message: `Template aplicado com sucesso! ${response.server?.commands?.length || template.commands.length} comandos adicionados ao host.`,
      })
    } catch (error) {
      console.error('Erro ao aplicar template:', error)
      setResult({
        success: false,
        message: 'Erro ao aplicar template. Por favor, tente novamente.',
      })
    } finally {
      setIsApplying(false)
    }
  }

  function handleClose() {
    setSelectedServer(null)
    setResult(null)
    onClose()
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className={`w-full max-w-md transform overflow-hidden rounded-2xl p-6 text-left align-middle shadow-xl transition-all ${
                actualTheme === 'dark' ? 'bg-gray-800' : 'bg-white'
              }`}>
                <Dialog.Title as="div" className="flex justify-between items-center mb-4">
                  <div className="flex items-center gap-2">
                    <BookTemplate className="h-6 w-6 text-primary-500" />
                    <h3 className={`text-lg font-medium ${
                      actualTheme === 'dark' ? 'text-white' : 'text-gray-900'
                    }`}>
                      Aplicar Template a um Host
                    </h3>
                  </div>
                  <button
                    onClick={handleClose}
                    className={actualTheme === 'dark' ? 'text-gray-400 hover:text-gray-300' : 'text-gray-400 hover:text-gray-500'}
                  >
                    <X className="h-5 w-5" />
                  </button>
                </Dialog.Title>

                <div className="mb-4">
                  <p className={`text-sm ${
                    actualTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                  }`}>
                    Selecione um host para aplicar o template <strong>{template.name}</strong>.
                    Os comandos do template serão adicionados ao host selecionado.
                  </p>
                </div>

                {result ? (
                  <div className={`p-4 mb-4 rounded-md ${
                    result.success
                      ? (actualTheme === 'dark' ? 'bg-green-900 text-green-200' : 'bg-green-50 text-green-800')
                      : (actualTheme === 'dark' ? 'bg-red-900 text-red-200' : 'bg-red-50 text-red-800')
                  }`}>
                    <div className="flex">
                      {result.success ? (
                        <Check className="h-5 w-5 text-green-400 mr-2" />
                      ) : (
                        <AlertTriangle className="h-5 w-5 text-red-400 mr-2" />
                      )}
                      <p className="text-sm">{result.message}</p>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="mb-4">
                      <label htmlFor="server" className={`block text-sm font-medium mb-1 ${
                        actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Host
                      </label>
                      {isLoading ? (
                        <div className={`animate-pulse h-10 rounded ${
                          actualTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'
                        }`}></div>
                      ) : servers.length === 0 ? (
                        <div className={`text-sm ${
                          actualTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                          Nenhum host disponível. Crie um host primeiro.
                        </div>
                      ) : (
                        <Listbox value={selectedServer} onChange={setSelectedServer}>
                          <div className="relative">
                            <Listbox.Button className={`relative w-full cursor-default rounded-md border py-2 pl-10 pr-10 text-left shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:text-sm ${
                              actualTheme === 'dark'
                                ? 'border-gray-600 bg-gray-700 text-gray-100'
                                : 'border-gray-300 bg-white text-gray-900'
                            }`}>
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <Server className={`h-5 w-5 ${
                                  actualTheme === 'dark' ? 'text-gray-400' : 'text-gray-400'
                                }`} />
                              </div>
                              <span className="block truncate">
                                {selectedServer ? `${selectedServer.name} (${selectedServer.host})` : 'Selecione um host'}
                              </span>
                              <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                <ChevronDown className={`h-5 w-5 ${
                                  actualTheme === 'dark' ? 'text-gray-400' : 'text-gray-400'
                                }`} aria-hidden="true" />
                              </span>
                            </Listbox.Button>

                            <Transition
                              as={Fragment}
                              leave="transition ease-in duration-100"
                              leaveFrom="opacity-100"
                              leaveTo="opacity-0"
                            >
                              <Listbox.Options className={`absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm ${
                                actualTheme === 'dark' ? 'bg-gray-700' : 'bg-white'
                              }`}>
                                {servers.map((server) => (
                                  <Listbox.Option
                                    key={server.id}
                                    className={({ active }) =>
                                      `relative cursor-default select-none py-2 pl-10 pr-4 ${
                                        active
                                          ? (actualTheme === 'dark' ? 'bg-blue-900/50 text-blue-200' : 'bg-blue-100 text-blue-900')
                                          : (actualTheme === 'dark' ? 'text-gray-100' : 'text-gray-900')
                                      }`
                                    }
                                    value={server}
                                  >
                                    {({ selected }) => (
                                      <>
                                        <span className={`block truncate ${selected ? 'font-medium' : 'font-normal'}`}>
                                          {server.name} ({server.host})
                                        </span>
                                        {selected ? (
                                          <span className={`absolute inset-y-0 left-0 flex items-center pl-3 ${
                                            actualTheme === 'dark' ? 'text-blue-400' : 'text-blue-600'
                                          }`}>
                                            <Check className="h-5 w-5" aria-hidden="true" />
                                          </span>
                                        ) : null}
                                      </>
                                    )}
                                  </Listbox.Option>
                                ))}
                              </Listbox.Options>
                            </Transition>
                          </div>
                        </Listbox>
                      )}
                    </div>

                    <div className={`mb-4 border-t pt-4 ${
                      actualTheme === 'dark' ? 'border-gray-600' : 'border-gray-200'
                    }`}>
                      <h4 className={`text-sm font-medium mb-2 flex items-center gap-1 ${
                        actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        <BookTemplate className={`h-4 w-4 ${
                          actualTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                        }`} />
                        Comandos no Template:
                      </h4>
                      <div className={`max-h-48 overflow-y-auto custom-scrollbar border rounded-lg p-3 ${
                        actualTheme === 'dark'
                          ? 'border-gray-600 bg-gray-700'
                          : 'border-gray-200 bg-gray-50'
                      }`}>
                        <ul className={`text-sm space-y-1 list-disc ml-4 ${
                          actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
                        }`}>
                          {template.commands.map((cmd) => (
                            <li key={cmd.id}>
                              <span className="font-medium">{cmd.name}</span>
                              {cmd.description && <span className={
                                actualTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                              }> - {cmd.description}</span>}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </>
                )}

                <div className={`flex justify-end gap-3 pt-4 border-t ${
                  actualTheme === 'dark' ? 'border-gray-600' : 'border-gray-200'
                }`}>
                  <button
                    type="button"
                    onClick={handleClose}
                    className={`inline-flex justify-center py-2 px-4 border shadow-sm text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                      actualTheme === 'dark'
                        ? 'border-gray-600 text-gray-300 bg-gray-700 hover:bg-gray-600'
                        : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                    }`}
                  >
                    {result ? 'Fechar' : 'Cancelar'}
                  </button>
                  
                  {!result && (
                    <button
                      type="button"
                      onClick={handleApplyTemplate}
                      disabled={!selectedServer || isApplying}
                      className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      {isApplying ? 'Aplicando...' : 'Aplicar Template'}
                    </button>
                  )}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
} 