import { api } from '../lib/axios'
import { TerminalTheme, CreateTerminalThemeDTO, UpdateTerminalThemeDTO } from '../types/terminalTheme'

export const terminalThemeService = {
  async getAll(): Promise<TerminalTheme[]> {
    const response = await api.get<TerminalTheme[]>('/api/terminal-themes')
    return response.data
  },

  async getById(id: string): Promise<TerminalTheme> {
    const response = await api.get<TerminalTheme>(`/api/terminal-themes/${id}`)
    return response.data
  },

  async create(data: CreateTerminalThemeDTO): Promise<TerminalTheme> {
    const response = await api.post<TerminalTheme>('/api/terminal-themes', data)
    return response.data
  },

  async update(id: string, data: UpdateTerminalThemeDTO): Promise<TerminalTheme> {
    const response = await api.put<TerminalTheme>(`/api/terminal-themes/${id}`, data)
    return response.data
  },

  async delete(id: string): Promise<void> {
    await api.delete(`/api/terminal-themes/${id}`)
  }
}
