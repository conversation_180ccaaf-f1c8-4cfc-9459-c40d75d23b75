import { NodeSSH } from 'node-ssh';
import { SSHServer } from '../../types/server';
import { SSHConfig } from '../../types/sshConfig';
import { SSHError } from '../../types/sshError';
import { Logger } from '../../utils/logger';
import { Device } from './deviceTypes';
import { LOOP_PROTECTION_CONFIG, calculateReconnectionDelay, isMemoryUsageHigh } from '../../config/loopProtection';

/**
 * Gerencia conexões SSH, incluindo conexão, reconexão e desconexão
 */
export class ConnectionManager {
  private ssh: NodeSSH;
  private isConnected: boolean = false;
  private connectionAttempts: number = 0;
  private maxConnectionAttempts: number = LOOP_PROTECTION_CONFIG.RECONNECTION.MAX_ATTEMPTS;
  private lastServer?: Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'>;
  private reconnectTimer?: NodeJS.Timeout;
  private connectionLock: boolean = false;
  private forceDisconnectTimer?: NodeJS.Timeout;
  private deviceType: Device = Device.GENERIC;

  // Circuit Breaker para prevenir loops infinitos de conexão
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private readonly CIRCUIT_BREAKER_THRESHOLD = LOOP_PROTECTION_CONFIG.CIRCUIT_BREAKER.FAILURE_THRESHOLD;
  private readonly CIRCUIT_BREAKER_TIMEOUT = LOOP_PROTECTION_CONFIG.CIRCUIT_BREAKER.TIMEOUT_MS;
  private readonly MAX_LOCK_WAIT_TIME = LOOP_PROTECTION_CONFIG.CONNECTION_LOCK.MAX_WAIT_TIME_MS;

  // Monitoramento de recursos
  private resourceMonitorTimer?: NodeJS.Timeout;

  constructor() {
    this.ssh = new NodeSSH();
    this.startResourceMonitoring();

    // Configurar handler global para erros não tratados
    process.on('uncaughtException', (error) => {
      Logger.error('Erro não tratado capturado pelo ConnectionManager:', error);
      this.handleConnectionError(error);
    });

    // Configurar handler para desconexão ao encerrar o processo
    process.on('SIGINT', () => {
      Logger.log('Processo interrompido, desconectando sessões SSH...');
      this.forceDisconnect();
      this.stopResourceMonitoring();
    });

    process.on('SIGTERM', () => {
      Logger.log('Processo terminado, desconectando sessões SSH...');
      this.forceDisconnect();
      this.stopResourceMonitoring();
    });
  }

  /**
   * Conecta ao servidor SSH
   * @param server Informações do servidor
   */
  async connect(server: Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<void> {
    // Verificar circuit breaker antes de tentar conectar
    if (this.isCircuitBreakerOpen()) {
      const timeRemaining = Math.ceil((this.CIRCUIT_BREAKER_TIMEOUT - (Date.now() - this.lastFailureTime)) / 1000);
      throw new Error(`Circuit breaker ativo. Muitas falhas de conexão recentes. Tente novamente em ${timeRemaining} segundos.`);
    }

    // Verificar se já existe uma conexão em andamento
    if (this.connectionLock) {
      Logger.log('Conexão SSH já em andamento, aguardando...');
      // Aguardar até que a conexão atual seja concluída ou falhe com timeout
      await new Promise<void>((resolve, reject) => {
        const startTime = Date.now();

        const checkLock = () => {
          if (!this.connectionLock) {
            resolve();
          } else if (Date.now() - startTime > this.MAX_LOCK_WAIT_TIME) {
            Logger.error(`Timeout aguardando liberação do lock de conexão após ${this.MAX_LOCK_WAIT_TIME}ms`);
            // Forçar liberação do lock em caso de timeout
            this.connectionLock = false;
            reject(new Error('Timeout aguardando liberação do lock de conexão'));
          } else {
            setTimeout(checkLock, 500);
          }
        };
        checkLock();
      });
    }

    // Adquirir o bloqueio de conexão
    this.connectionLock = true;

    try {
      // Verificar se já existe uma conexão ativa e encerrá-la
      if (this.isConnected) {
        Logger.log('Conexão SSH já existente, desconectando antes de criar uma nova...');
        await this.disconnect();
      }

      // Limpar qualquer tentativa de reconexão pendente
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = undefined;
      }

      // Armazenar informações do servidor para possível reconexão
      this.lastServer = server;
      this.connectionAttempts++;

      // Usar o tipo de dispositivo selecionado pelo usuário
      this.deviceType = (server.deviceType as Device) || Device.GENERIC;

      Logger.log(`Iniciando conexão SSH com ${server.host}:${server.port} (${server.name}) - Tentativa ${this.connectionAttempts}`);
      Logger.log(`Tipo de dispositivo: ${this.deviceType}`);

      // Criar configuração base
      const config: SSHConfig = this.createSSHConfig(server);

      try {
        await this.ssh.connect(config);
        Logger.log(`Conexão SSH estabelecida com sucesso: ${server.host}`);

        // Resetar contador de tentativas e circuit breaker após conexão bem-sucedida
        this.connectionAttempts = 0;
        this.failureCount = 0;
        this.isConnected = true;

        // Configurar handlers para eventos de conexão
        this.setupConnectionHandlers();
      } catch (sshError: any) {
        // Tratamento especial para dispositivos Datacom OS antigos
        if (this.deviceType === Device.DATACOM) {
          // Tentar configuração SSH legacy para dispositivos Datacom
          if (sshError.message?.includes('no matching key exchange algorithm') ||
              sshError.message?.includes('Handshake failed') ||
              sshError.message?.includes('All configured authentication methods failed')) {

            Logger.warn(`Falha SSH em dispositivo Datacom OS: ${sshError.message}`);
            Logger.log('Tentando configuração SSH legacy com keyboard-interactive...');

            // Tentar com configuração ainda mais conservadora
            const legacyConfig = this.createLegacyDatacomConfig(server);

            await this.ssh.connect(legacyConfig);

            Logger.log(`Conexão SSH estabelecida com configuração legacy: ${server.host}`);

            // Resetar contador de tentativas e circuit breaker após conexão bem-sucedida
            this.connectionAttempts = 0;
            this.failureCount = 0;
            this.isConnected = true;

            // Configurar handlers para eventos de conexão
            this.setupConnectionHandlers();
          } else {
            // Re-lançar outros erros SSH
            throw sshError;
          }
        } else {
          // Re-lançar o erro se não for um dispositivo DATACOM
          throw sshError;
        }
      }

      // Configurar um timer para forçar a desconexão após um período máximo
      // Isso evita que sessões fiquem abertas indefinidamente
      this.setupForceDisconnectTimer();
    } catch (error) {
      Logger.error(`Erro ao conectar via SSH com ${server.host}:`, error);
      this.isConnected = false;

      // Incrementar contador de falhas para circuit breaker
      this.failureCount++;
      this.lastFailureTime = Date.now();
      Logger.log(`Falha de conexão registrada. Total de falhas: ${this.failureCount}`);

      // Tentar reconectar se não excedeu o número máximo de tentativas
      if (this.connectionAttempts < this.maxConnectionAttempts && !this.isCircuitBreakerOpen()) {
        const delay = calculateReconnectionDelay(this.connectionAttempts);
        Logger.log(`Tentando reconectar em ${delay}ms... (Tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts})`);
        return this.scheduleReconnect(delay);
      } else {
        this.connectionAttempts = 0;
        // Liberar o bloqueio de conexão antes de lançar o erro
        this.connectionLock = false;

        if (this.isCircuitBreakerOpen()) {
          throw new Error(`Circuit breaker ativo devido a muitas falhas de conexão. Aguarde ${Math.ceil(this.CIRCUIT_BREAKER_TIMEOUT / 1000)} segundos antes de tentar novamente.`);
        } else {
          throw new Error(`Falha ao conectar via SSH após ${this.maxConnectionAttempts} tentativas: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
        }
      }
    } finally {
      // Garantir que o bloqueio de conexão seja liberado
      this.connectionLock = false;
    }
  }

  /**
   * Cria a configuração SSH com base no tipo de dispositivo
   * @param server Informações do servidor
   * @returns Configuração SSH
   */
  private createSSHConfig(server: Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'>): SSHConfig {
    // Configuração base
    const config: SSHConfig = {
      host: server.host,
      port: server.port,
      username: server.username,
      readyTimeout: 20000, // 20 segundos para timeout de conexão
      keepaliveInterval: 5000, // 5 segundos para keepalive
      keepaliveCountMax: 3, // Número máximo de pacotes keepalive sem resposta
      reconnect: false // Gerenciamos a reconexão manualmente
    };

    // Configurar autenticação
    if (server.privateKey) {
      config.privateKey = server.privateKey;
    } else if (server.password) {
      config.password = server.password;
    } else {
      throw new Error('Nenhum método de autenticação fornecido');
    }

    // Aplicar configurações específicas por tipo de dispositivo
    switch (this.deviceType) {
      case Device.HUAWEI:
        // Configurações mais conservadoras para Huawei
        config.readyTimeout = 30000; // 30 segundos para timeout de conexão
        config.keepaliveInterval = 10000; // 10 segundos para keepalive
        config.keepaliveCountMax = 5; // Mais pacotes keepalive sem resposta

        // Algoritmos compatíveis com Huawei
        config.algorithms = {
          kex: [
            'diffie-hellman-group-exchange-sha1',
            'diffie-hellman-group14-sha1',
            'diffie-hellman-group1-sha1'
          ],
          cipher: [
            'aes128-ctr',
            'aes192-ctr',
            'aes256-ctr',
            'aes128-gcm',
            '<EMAIL>',
            'aes256-gcm',
            '<EMAIL>'
          ],
          serverHostKey: [
            'ssh-rsa',
            'ssh-dss',
            'ecdsa-sha2-nistp256',
            'ecdsa-sha2-nistp384',
            'ecdsa-sha2-nistp521'
          ]
        };
        break;

      case Device.MIKROTIK:
        // Configurações otimizadas para Mikrotik
        config.readyTimeout = 180000; // 180 segundos para timeout de conexão (aumentado)

        // Desativar keepalive automático da biblioteca SSH2
        // Isso evita o problema de "Keepalive timeout" causado pela forma como o Mikrotik responde
        config.keepaliveInterval = 0; // Desativar keepalive automático
        config.keepaliveCountMax = 0; // Desativar keepalive automático

        // Algoritmos compatíveis com Mikrotik
        config.algorithms = {
          kex: [
            'diffie-hellman-group-exchange-sha256',
            'diffie-hellman-group14-sha256',
            'diffie-hellman-group16-sha512',
            'diffie-hellman-group18-sha512',
            'diffie-hellman-group-exchange-sha1',
            'diffie-hellman-group14-sha1',
            'diffie-hellman-group1-sha1'
          ],
          cipher: [
            'aes128-ctr',
            'aes192-ctr',
            'aes256-ctr',
            'aes128-gcm',
            '<EMAIL>',
            'aes256-gcm',
            '<EMAIL>'
          ],
          serverHostKey: [
            'ssh-rsa',
            'ssh-dss',
            'ecdsa-sha2-nistp256',
            'ecdsa-sha2-nistp384',
            'ecdsa-sha2-nistp521'
          ]
          // A propriedade hmac não é suportada pelo tipo SSHConfig
          // Removido para evitar erros de compilação
        };
        break;

      case Device.DMOS:
        // Configurações mais conservadoras para DmOS
        config.readyTimeout = 35000; // 35 segundos para timeout de conexão
        config.keepaliveInterval = 8000; // 8 segundos para keepalive
        config.keepaliveCountMax = 4; // Número de pacotes keepalive sem resposta
        break;

      case Device.DATACOM:
        // Configurações específicas para dispositivos Datacom OS antigos
        config.readyTimeout = 60000; // 60 segundos para timeout de conexão (dispositivos muito antigos)
        config.keepaliveInterval = 15000; // 15 segundos para keepalive
        config.keepaliveCountMax = 3; // Número de pacotes keepalive sem resposta

        // Habilitar keyboard-interactive authentication para dispositivos Datacom
        (config as any).tryKeyboard = true;

        // Configurar handler para keyboard-interactive authentication
        (config as any).onKeyboardInteractive = (name: string, instructions: string, _instructionsLang: string, prompts: any[], finish: (responses: string[]) => void) => {
          Logger.log(`DATACOM keyboard-interactive solicitado:`);
          Logger.log(`- Nome: ${name}`);
          Logger.log(`- Instruções: ${instructions}`);
          Logger.log(`- Prompts: ${prompts.length} prompt(s)`);

          // Responder com a senha para todos os prompts
          const responses: string[] = [];
          for (let i = 0; i < prompts.length; i++) {
            const prompt = prompts[i];
            Logger.log(`- Prompt ${i + 1}: ${prompt.prompt} (echo: ${prompt.echo})`);

            // Usar a senha do servidor para responder ao prompt
            if (server.password) {
              responses.push(server.password);
            } else {
              Logger.warn('Nenhuma senha disponível para responder ao prompt keyboard-interactive');
              responses.push('');
            }
          }

          Logger.log(`Respondendo keyboard-interactive com ${responses.length} resposta(s)`);
          finish(responses);
        };

        // Algoritmos ultra-conservadores para Datacom OS antigo
        config.algorithms = {
          kex: [
            // Apenas algoritmos muito antigos para máxima compatibilidade
            'diffie-hellman-group1-sha1',
            'diffie-hellman-group14-sha1',
            'diffie-hellman-group-exchange-sha1'
          ],
          cipher: [
            // Ciphers básicos e antigos primeiro
            '3des-cbc',
            'aes128-cbc',
            'aes192-cbc',
            'aes256-cbc',
            'aes128-ctr',
            'aes192-ctr',
            'aes256-ctr'
          ],
          serverHostKey: [
            // Apenas chaves de host básicas
            'ssh-rsa',
            'ssh-dss'
          ]
        };
        break;
    }

    return config;
  }



  /**
   * Cria uma configuração SSH ultra-conservadora para dispositivos Datacom OS muito antigos
   * @param server Informações do servidor
   * @returns Configuração SSH legacy
   */
  private createLegacyDatacomConfig(server: Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'>): SSHConfig {
    const config: SSHConfig = {
      host: server.host,
      port: server.port,
      username: server.username,
      readyTimeout: 60000, // 60 segundos para timeout de conexão
      keepaliveInterval: 15000, // 15 segundos para keepalive
      keepaliveCountMax: 3, // Número máximo de pacotes keepalive sem resposta
      reconnect: false // Gerenciamos a reconexão manualmente
    };

    // Configurar autenticação
    if (server.privateKey) {
      config.privateKey = server.privateKey;
    } else if (server.password) {
      config.password = server.password;
    } else {
      throw new Error('Nenhum método de autenticação fornecido');
    }

    // Habilitar keyboard-interactive authentication para dispositivos Datacom legacy
    (config as any).tryKeyboard = true;

    // Configurar handler para keyboard-interactive authentication
    (config as any).onKeyboardInteractive = (name: string, instructions: string, _instructionsLang: string, prompts: any[], finish: (responses: string[]) => void) => {
      Logger.log(`DATACOM Legacy keyboard-interactive solicitado:`);
      Logger.log(`- Nome: ${name}`);
      Logger.log(`- Instruções: ${instructions}`);
      Logger.log(`- Prompts: ${prompts.length} prompt(s)`);

      // Responder com a senha para todos os prompts
      const responses: string[] = [];
      for (let i = 0; i < prompts.length; i++) {
        const prompt = prompts[i];
        Logger.log(`- Prompt ${i + 1}: ${prompt.prompt} (echo: ${prompt.echo})`);

        // Usar a senha do servidor para responder ao prompt
        if (server.password) {
          responses.push(server.password);
        } else {
          Logger.warn('Nenhuma senha disponível para responder ao prompt keyboard-interactive');
          responses.push('');
        }
      }

      Logger.log(`Respondendo keyboard-interactive com ${responses.length} resposta(s)`);
      finish(responses);
    };

    // Algoritmos ultra-conservadores para dispositivos Datacom OS muito antigos
    config.algorithms = {
      kex: [
        // Apenas os algoritmos mais antigos e amplamente suportados
        'diffie-hellman-group1-sha1',
        'diffie-hellman-group14-sha1'
      ],
      cipher: [
        // Apenas ciphers básicos e antigos
        '3des-cbc',
        'aes128-cbc',
        'aes192-cbc',
        'aes256-cbc'
      ],
      serverHostKey: [
        // Apenas chaves de host básicas
        'ssh-rsa',
        'ssh-dss'
      ]
    };

    Logger.log('Configuração SSH legacy criada para dispositivo Datacom OS antigo');
    return config;
  }

  /**
   * Configura handlers para eventos de conexão
   */
  private setupConnectionHandlers(): void {
    // Configurar handlers para eventos de conexão
    this.ssh.connection?.on('error', (error: any) => {
      Logger.error('Erro na conexão SSH:', error);
      this.handleConnectionError(error);
    });

    this.ssh.connection?.on('close', () => {
      Logger.log('Conexão SSH fechada');
      this.isConnected = false;

      // Limpar o timer de desconexão forçada
      if (this.forceDisconnectTimer) {
        clearTimeout(this.forceDisconnectTimer);
        this.forceDisconnectTimer = undefined;
      }

      // Desabilitar reconexão automática para evitar acúmulo de sessões
      // Especialmente importante para dispositivos Nokia
      if (this.deviceType === Device.NOKIA) {
        Logger.log('Dispositivo Nokia: desabilitando reconexão automática');
        return;
      }

      // Tentar reconectar automaticamente apenas se não for Nokia
      if (this.lastServer && this.connectionAttempts < this.maxConnectionAttempts) {
        this.scheduleReconnect(5000);
      }
    });

    this.ssh.connection?.on('timeout', () => {
      Logger.log('Timeout na conexão SSH');
      this.isConnected = false;

      // Limpar o timer de desconexão forçada
      if (this.forceDisconnectTimer) {
        clearTimeout(this.forceDisconnectTimer);
        this.forceDisconnectTimer = undefined;
      }

      // Desabilitar reconexão automática para dispositivos Nokia
      if (this.deviceType === Device.NOKIA) {
        Logger.log('Dispositivo Nokia: desabilitando reconexão automática após timeout');
        return;
      }

      // Tentar reconectar automaticamente apenas se não for Nokia
      if (this.lastServer && this.connectionAttempts < this.maxConnectionAttempts) {
        this.scheduleReconnect(5000);
      }
    });
  }



  /**
   * Trata erros de conexão
   * @param error Erro de conexão
   */
  private handleConnectionError(error: SSHError | any): void {
    Logger.error('Tratando erro de conexão SSH:', error);
    this.isConnected = false;

    // Limpar o timer de desconexão forçada
    if (this.forceDisconnectTimer) {
      clearTimeout(this.forceDisconnectTimer);
      this.forceDisconnectTimer = undefined;
    }

    // Limpar recursos
    try {
      this.ssh.dispose();
    } catch (e) {
      Logger.error('Erro ao limpar recursos SSH:', e);
    }

    // Criar nova instância SSH
    this.ssh = new NodeSSH();

    // Desabilitar reconexão automática para dispositivos Nokia
    if (this.deviceType === Device.NOKIA) {
      Logger.log('Dispositivo Nokia: desabilitando reconexão automática após erro');
      return;
    }

    // Tentar reconectar se tiver informações do servidor e não for Nokia
    if (this.lastServer && this.connectionAttempts < this.maxConnectionAttempts) {
      this.scheduleReconnect(5000);
    }
  }

  /**
   * Agenda uma reconexão após um atraso
   * @param delay Atraso em milissegundos
   */
  private scheduleReconnect(delay: number): Promise<void> {
    return new Promise((resolve) => {
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }

      this.reconnectTimer = setTimeout(async () => {
        if (this.lastServer) {
          try {
            await this.connect(this.lastServer);
            resolve();
          } catch (error) {
            Logger.error('Falha na tentativa de reconexão:', error);
            // Não resolvemos a promise em caso de falha para não dar falsa impressão de sucesso
          }
        }
      }, delay);
    });
  }

  /**
   * Configura um timer para forçar a desconexão após um período máximo
   */
  private setupForceDisconnectTimer(): void {
    // Limpar timer existente, se houver
    if (this.forceDisconnectTimer) {
      clearTimeout(this.forceDisconnectTimer);
    }

    // Definir um tempo máximo para a sessão
    const MAX_SESSION_TIME = LOOP_PROTECTION_CONFIG.SESSION_MANAGEMENT.MAX_SESSION_TIME_MS;

    this.forceDisconnectTimer = setTimeout(() => {
      Logger.log('Tempo máximo de sessão atingido, forçando desconexão...');
      this.forceDisconnect();
    }, MAX_SESSION_TIME);
  }

  /**
   * Força a desconexão e limpa todos os recursos
   */
  private forceDisconnect(): void {
    Logger.log('Forçando desconexão da sessão SSH...');

    // Limpar timers
    if (this.forceDisconnectTimer) {
      clearTimeout(this.forceDisconnectTimer);
      this.forceDisconnectTimer = undefined;
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = undefined;
    }

    // Desconectar e limpar recursos
    try {
      // Tratamento especial para dispositivos Nokia antes de desconectar
      if (this.deviceType === Device.NOKIA && this.isConnected && this.ssh.connection) {
        try {
          Logger.log('Dispositivo Nokia: desconectando sem enviar comando de logout');
          // Para Nokia, não enviar comando de logout para evitar o erro "Unable to exec"
          // Apenas desconectar diretamente
          this.ssh.dispose();
        } catch (e) {
          Logger.error('Erro ao desconectar Nokia:', e);
          // Garantir que a desconexão ocorra mesmo em caso de erro
          try {
            this.ssh.dispose();
          } catch (disposeError) {
            Logger.error('Erro secundário ao desconectar Nokia:', disposeError);
          }
        }
      }
      // Enviar comando de quit para dispositivos Mikrotik antes de desconectar
      else if (this.deviceType === Device.MIKROTIK && this.isConnected && this.ssh.connection) {
        try {
          Logger.log('Enviando comando de quit para dispositivo Mikrotik antes de desconectar');
          // Tentar enviar comando de quit, mas não aguardar resposta
          this.ssh.execCommand('quit', { cwd: '/' }).catch(e => {
            Logger.error('Erro ao enviar comando de quit:', e);
          });

          // Pequena pausa para permitir que o comando seja processado
          setTimeout(() => {
            this.ssh.dispose();
          }, 1000);
        } catch (e) {
          Logger.error('Erro ao enviar quit para Mikrotik:', e);
          this.ssh.dispose();
        }
      } else {
        this.ssh.dispose();
      }
    } catch (e) {
      Logger.error('Erro ao forçar desconexão SSH:', e);
    } finally {
      // Garantir que o estado seja resetado
      this.isConnected = false;
      this.connectionAttempts = 0;
      this.ssh = new NodeSSH();
      this.connectionLock = false;
    }
  }

  /**
   * Desconecta do servidor SSH
   */
  async disconnect(): Promise<void> {
    try {
      // Limpar qualquer tentativa de reconexão pendente
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = undefined;
      }

      // Limpar o timer de desconexão forçada
      if (this.forceDisconnectTimer) {
        clearTimeout(this.forceDisconnectTimer);
        this.forceDisconnectTimer = undefined;
      }

      Logger.log('Desconectando sessão SSH');

      // Tratamento especial para dispositivos Nokia antes de desconectar
      if (this.deviceType === Device.NOKIA && this.isConnected && this.ssh.connection) {
        try {
          Logger.log('Dispositivo Nokia: desconectando sem enviar comando de logout');
          // Para Nokia, não enviar comando de logout para evitar o erro "Unable to exec"
          // Apenas registrar a intenção de desconectar

          // Pequena pausa antes de desconectar para permitir que qualquer operação pendente seja concluída
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (e) {
          Logger.error('Erro ao preparar desconexão do Nokia:', e);
          // Não propagar o erro, apenas registrar
        }
      }

      // Para dispositivos Mikrotik, enviar comando de quit antes de desconectar
      if (this.deviceType === Device.MIKROTIK && this.isConnected && this.ssh.connection) {
        try {
          Logger.log('Enviando comando de quit para dispositivo Mikrotik');
          // Executar comando de quit e aguardar um curto período
          await this.ssh.execCommand('quit', { cwd: '/' });

          // Pequena pausa para permitir que o comando seja processado
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (e) {
          Logger.error('Erro ao enviar quit para Mikrotik:', e);
        }
      }

      // Desconectar e limpar recursos
      this.ssh.dispose();
      this.isConnected = false;
      this.connectionAttempts = 0;
      this.ssh = new NodeSSH();
      Logger.log('Sessão SSH encerrada com sucesso');
    } catch (error) {
      Logger.error('Erro ao desconectar sessão SSH:', error);
      // Garantir que os recursos sejam liberados mesmo em caso de erro
      this.isConnected = false;
      this.connectionAttempts = 0;
      this.ssh = new NodeSSH();
    }
  }

  /**
   * Verifica se está conectado e pronto para executar comandos
   */
  isConnectedAndReady(): boolean {
    return this.isConnected && !!this.ssh.connection && !this.ssh.connection.closing;
  }

  /**
   * Obtém a instância SSH
   */
  getSSH(): NodeSSH {
    return this.ssh;
  }

  /**
   * Obtém o tipo de dispositivo
   */
  getDeviceType(): Device {
    return this.deviceType;
  }

  /**
   * Obtém o último servidor conectado
   */
  getLastServer(): Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'> | undefined {
    return this.lastServer;
  }

  /**
   * Verifica se o circuit breaker está aberto (muitas falhas recentes)
   */
  private isCircuitBreakerOpen(): boolean {
    if (this.failureCount >= this.CIRCUIT_BREAKER_THRESHOLD) {
      const timeSinceLastFailure = Date.now() - this.lastFailureTime;
      const isOpen = timeSinceLastFailure < this.CIRCUIT_BREAKER_TIMEOUT;

      if (isOpen) {
        Logger.warn(`Circuit breaker ativo. Falhas: ${this.failureCount}, Tempo restante: ${Math.ceil((this.CIRCUIT_BREAKER_TIMEOUT - timeSinceLastFailure) / 1000)}s`);
      } else {
        // Reset do circuit breaker após timeout
        Logger.log('Circuit breaker resetado após timeout');
        this.failureCount = 0;
      }

      return isOpen;
    }
    return false;
  }

  /**
   * Inicia o monitoramento de recursos para prevenir vazamentos de memória
   */
  private startResourceMonitoring(): void {
    // Verificar recursos usando configuração centralizada
    this.resourceMonitorTimer = setInterval(() => {
      this.monitorResources();
    }, LOOP_PROTECTION_CONFIG.SESSION_MANAGEMENT.RESOURCE_MONITOR_INTERVAL_MS);

    Logger.log('Monitoramento de recursos iniciado');
  }

  /**
   * Para o monitoramento de recursos
   */
  private stopResourceMonitoring(): void {
    if (this.resourceMonitorTimer) {
      clearInterval(this.resourceMonitorTimer);
      this.resourceMonitorTimer = undefined;
      Logger.log('Monitoramento de recursos parado');
    }
  }

  /**
   * Monitora o uso de recursos e força limpeza se necessário
   */
  private monitorResources(): void {
    const memUsage = process.memoryUsage();
    const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
    const heapTotalMB = Math.round(memUsage.heapTotal / 1024 / 1024);

    Logger.log(`Uso de memória: ${heapUsedMB}MB / ${heapTotalMB}MB`);

    // Verificar limite usando configuração centralizada
    if (isMemoryUsageHigh()) {
      Logger.warn(`Alto uso de memória detectado (${heapUsedMB}MB), forçando limpeza de recursos`);
      this.forceCleanup();
    }

    // Verificar se há conexões órfãs (conectadas há mais de 15 minutos)
    if (this.isConnected && this.forceDisconnectTimer) {
      Logger.log('Verificando tempo de vida da conexão SSH');
    }
  }

  /**
   * Força a limpeza de recursos em caso de alto uso de memória
   */
  private forceCleanup(): void {
    Logger.warn('Executando limpeza forçada de recursos');

    try {
      // Forçar garbage collection se disponível
      if (global.gc) {
        global.gc();
        Logger.log('Garbage collection executado');
      }

      // Desconectar se estiver conectado há muito tempo
      if (this.isConnected) {
        Logger.warn('Forçando desconexão devido ao alto uso de memória');
        this.forceDisconnect();
      }

      // Reset do circuit breaker em caso de limpeza forçada
      this.failureCount = Math.max(0, this.failureCount - 1);

    } catch (error) {
      Logger.error('Erro durante limpeza forçada:', error);
    }
  }
}
