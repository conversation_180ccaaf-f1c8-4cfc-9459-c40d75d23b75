import { CommandResult } from '../../../types/server';
import { Logger } from '../../../utils/logger';
import * as Telnet from 'telnet-client';

/**
 * Executor de comandos para dispositivos Datacom OS (EDDs)
 * Implementação usando Telnet para melhor compatibilidade com dispositivos Datacom
 */
export class DatacomExecutor {
  private telnetClient: Telnet.Telnet;
  private lastCommandTime: number = 0;
  private keepaliveInterval: NodeJS.Timeout | null = null;
  private isConnected: boolean = false;
  private commandQueue: Promise<any> = Promise.resolve(); // Fila de comandos
  private connectionParams: any;

  // Configurações de timeout
  private readonly BASE_TIMEOUT = 45000; // 45 segundos como base
  private readonly TIMEOUT_PER_COMMAND = 8000; // 8 segundos adicionais por comando
  private readonly MAX_TIMEOUT = 120000; // Limite máximo de 2 minutos
  private readonly PING_TIMEOUT = 90000; // 90 segundos específico para comandos PING

  constructor(host: string, port: number = 23, username?: string, password?: string) {
    this.telnetClient = new Telnet.Telnet();
    this.connectionParams = {
      host,
      port,
      username,
      password,
      shellPrompt: /[#$>]\s*$/,
      timeout: 30000,
      loginPrompt: /login[: ]*$/i,
      passwordPrompt: /password[: ]*$/i,
      failedLoginMatch: /login failed|incorrect|denied/i,
      echoLines: 1
    };

    // Inicializar keepalive
    this.startKeepalive();
  }

  /**
   * Conecta ao dispositivo Datacom via Telnet
   */
  async connect(): Promise<void> {
    if (this.isConnected) {
      return;
    }

    try {
      Logger.log(`Conectando ao dispositivo Datacom via Telnet: ${this.connectionParams.host}:${this.connectionParams.port}`);

      await this.telnetClient.connect(this.connectionParams);
      this.isConnected = true;

      Logger.log('Conexão Telnet estabelecida com sucesso');
    } catch (error) {
      Logger.error('Erro ao conectar via Telnet:', error);
      throw new Error(`Falha na conexão Telnet: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Inicia keepalive para manter a conexão Telnet ativa
   */
  private startKeepalive(): void {
    if (this.keepaliveInterval) {
      return; // Já está rodando
    }

    Logger.log('Iniciando keepalive para conexão Telnet Datacom OS a cada 120 segundos');

    this.keepaliveInterval = setInterval(async () => {
      try {
        // Verificar se passou tempo suficiente desde o último comando
        const timeSinceLastCommand = Date.now() - this.lastCommandTime;

        // Só enviar keepalive se não houve comando recente (últimos 100 segundos)
        if (timeSinceLastCommand > 100000 && this.isConnected) {
          Logger.log('Enviando keepalive via Telnet');
          await this.telnetClient.send('\n'); // Enviar enter para manter ativo
        }
      } catch (error) {
        // Ignorar erros de keepalive silenciosamente
        Logger.log('Keepalive falhou, conexão pode ter sido perdida');
      }
    }, 120000); // A cada 2 minutos
  }

  /**
   * Para o keepalive manual
   */
  private stopKeepalive(): void {
    if (this.keepaliveInterval) {
      clearInterval(this.keepaliveInterval);
      this.keepaliveInterval = null;
      Logger.log('Parando keepalive manual para Datacom OS');
    }
  }

  /**
   * Limpa todos os recursos (método público para limpeza externa)
   */
  public cleanup(): void {
    this.stopKeepalive();

    // Fechar conexão Telnet
    if (this.isConnected) {
      try {
        this.telnetClient.end();
        this.isConnected = false;
        Logger.log('Conexão Telnet fechada');
      } catch (e) {
        Logger.error('Erro ao fechar conexão Telnet:', e);
      }
    }
  }

  /**
   * Calcula timeout dinâmico baseado no número de comandos
   */
  private calculateDynamicTimeout(commandCount: number): number {
    const timeout = this.BASE_TIMEOUT + (commandCount * this.TIMEOUT_PER_COMMAND);
    return Math.min(timeout, this.MAX_TIMEOUT);
  }

  /**
   * Executa um comando via Telnet
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  private async executeTelnetCommand(command: string): Promise<CommandResult> {
    try {
      // Garantir que estamos conectados
      await this.connect();

      Logger.log(`Executando comando Telnet: ${command}`);

      // Enviar comando e aguardar resposta
      const response = await this.telnetClient.exec(command, {
        timeout: this.calculateDynamicTimeout(1)
      });

      return {
        stdout: response || '',
        stderr: '',
        code: 0
      };
    } catch (error) {
      Logger.error('Erro ao executar comando Telnet:', error);

      // Se houve erro de conexão, tentar reconectar
      if (error instanceof Error && error.message.includes('connection')) {
        this.isConnected = false;
        try {
          await this.connect();
          // Tentar novamente após reconexão
          const response = await this.telnetClient.exec(command, {
            timeout: this.calculateDynamicTimeout(1)
          });
          return {
            stdout: response || '',
            stderr: '',
            code: 0
          };
        } catch (retryError) {
          Logger.error('Erro na tentativa de reconexão:', retryError);
        }
      }

      return {
        stdout: '',
        stderr: error instanceof Error ? error.message : 'Erro desconhecido',
        code: 1
      };
    }
  }

  /**
   * Executa comando PING com timeout estendido e tratamento específico
   * @param command Comando PING a ser executado
   * @returns Resultado do comando
   */
private async executePingCommand(command: string): Promise<CommandResult> {
  try {
    await this.connect();

    Logger.log(`Executando comando PING Datacom via .send(): ${command}`);

    // Usar timeout mais longo e aguardar prompt de retorno ao invés de padrões específicos
    const output = await this.telnetClient.send(command + '\n', {
      waitfor: /[#$>%]\s*$/m, // Aguardar prompt de retorno
      timeout: this.PING_TIMEOUT,
      newlineReplace: '\n'
    });

    const isPingComplete = this.isPingComplete(output);

    if (isPingComplete) {
      Logger.log('Comando PING Datacom completado com sucesso via send()');
    } else {
      Logger.log('PING via send() pode estar incompleto - verificar saída');
    }

    return {
      stdout: output,
      stderr: '',
      code: 0
    };
  } catch (error) {
    Logger.error('Erro ao executar comando PING via send():', error);

    return {
      stdout: '',
      stderr: error instanceof Error ? error.message : 'Erro desconhecido',
      code: 1
    };
  }
}

  /**
   * Verifica se o comando PING foi completado baseado na saída
   * @param output Saída do comando
   * @returns true se o PING foi completado
   */
  private isPingComplete(output: string): boolean {
    const lowerOutput = output.toLowerCase();

    // Verificar se há estatísticas finais (mais rigoroso)
    const hasStatistics = lowerOutput.includes('packets transmitted') &&
                         (lowerOutput.includes('received') || lowerOutput.includes('packet loss'));

    // Verificar se há seção de estatísticas
    const hasStatisticsSection = lowerOutput.includes('--- ping statistics ---') ||
                                lowerOutput.includes('ping statistics') ||
                                lowerOutput.includes('rtt min/avg/max');

    // Verificar se há erro definitivo
    const hasError = lowerOutput.includes('network is unreachable') ||
                    lowerOutput.includes('destination host unreachable') ||
                    lowerOutput.includes('no route to host') ||
                    lowerOutput.includes('connect: network is unreachable');

    // Verificar se há pelo menos algumas respostas de ping E estatísticas
    const hasResponses = lowerOutput.includes('bytes from') || lowerOutput.includes('icmp_seq=');
    const hasPrompt = output.match(/[#$>%]\s*$/m);

    Logger.log(`PING completion check: stats=${hasStatistics}, section=${hasStatisticsSection}, error=${hasError}, responses=${hasResponses}, prompt=${!!hasPrompt}`);

    // Só considerar completo se tiver estatísticas finais OU erro definitivo
    return hasStatistics || hasStatisticsSection || hasError;
  }



  /**
   * Executa um comando em um dispositivo Datacom OS via Telnet (com fila para evitar sobrecarga)
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  async executeCommand(command: string): Promise<CommandResult> {
    // Usar fila de comandos para evitar sobrecarga do dispositivo
    return new Promise((resolve, reject) => {
      this.commandQueue = this.commandQueue.then(async () => {
        try {
          // Atualizar timestamp do último comando
          this.lastCommandTime = Date.now();

          // Verificar se o comando contém múltiplas linhas
          if (command.includes('\n')) {
            Logger.log('Detectado comando com múltiplas linhas em dispositivo Datacom OS, usando modo especial');
            const result = await this.executeDatacomMultilineCommand(command);
            resolve(result);
            return;
          }

          // Limpar o comando para remover caracteres invisíveis e espaços extras
          const cleanCommand = command.trim().replace(/\s+/g, ' ');

          Logger.log(`Executando comando Datacom OS via Telnet: ${cleanCommand.substring(0, 50)}${cleanCommand.length > 50 ? '...' : ''}`);

          // Detectar comandos PING e usar método específico
          const isPing = cleanCommand.toLowerCase().includes('ping');

          if (isPing) {
            Logger.log('Detectado comando PING, usando executor específico com timeout estendido');
            const result = await this.executePingCommand(cleanCommand);
            resolve(result);
            return;
          }

          // Executar comando via Telnet (método padrão)
          const result = await this.executeTelnetCommand(cleanCommand);
          resolve(result);
        } catch (error) {
          Logger.error('Erro ao executar comando Datacom OS via Telnet:', error);
          reject(new Error(`Falha ao executar comando Datacom OS via Telnet: ${error instanceof Error ? error.message : 'Erro desconhecido'}`));
        }
      }).catch(reject);
    });
  }




  /**
   * Executa um comando com múltiplas linhas em um dispositivo Datacom OS via Telnet
   * @param command Comando com múltiplas linhas
   * @returns Resultado do comando
   */
  private async executeDatacomMultilineCommand(command: string): Promise<CommandResult> {
    Logger.log('Executando comando multilinhas para Datacom OS via Telnet');

    const lines = command.split('\n').filter(line => line.trim() !== '');
    const commandCount = lines.length;

    Logger.log(`Executando ${commandCount} comandos Datacom OS separados via Telnet`);

    // Executar comandos sequencialmente via Telnet
    let combinedOutput = '';
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      Logger.log(`Executando linha ${i + 1}/${lines.length}: ${line}`);

      try {
        // Detectar se a linha é um comando PING
        const isPingLine = line.toLowerCase().includes('ping');

        let result;
        if (isPingLine) {
          Logger.log(`Linha ${i + 1} é um comando PING, usando executor específico`);
          result = await this.executePingCommand(line);
        } else {
          result = await this.executeTelnetCommand(line);
        }

        combinedOutput += result.stdout + '\n';

        // Pausa entre comandos para não sobrecarregar
        if (i < lines.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      } catch (error) {
        Logger.error(`Erro na linha ${i + 1}: ${error}`);
        combinedOutput += `Erro na linha ${i + 1}: ${error}\n`;
      }
    }

    return {
      stdout: combinedOutput,
      stderr: '',
      code: 0
    };
  }

}
