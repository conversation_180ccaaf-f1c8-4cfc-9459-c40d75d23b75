# Scripts de Automação

Esta pasta contém scripts shell para automação e manutenção do projeto.

## Scripts Disponíveis

### `backup-externo.sh`
Script para realizar backup externo dos dados do sistema.

### `configurar-backup-diario.sh`
Script para configurar backup diário automático no sistema.

### `instalar-backup-producao.sh`
Script para instalar e configurar backup no ambiente de produção.

### `monitor.sh`
Script de monitoramento do sistema e serviços.

### `update.sh`
Script para atualização do sistema e dependências.

### `verificar-backups.sh`
Script para verificar o status completo do sistema de backup, incluindo containers, cron e serviços.

### `setup-servidor.sh`
Script para configuração automática completa do sistema em um novo servidor Linux. Configura backup, inicialização automática e todos os serviços necessários.

### `sem-fronteiras.service`
Arquivo de configuração do serviço systemd para inicialização automática da aplicação.

## Como Usar

### Setup Automático em Novo Servidor

Para configurar automaticamente todo o sistema em um novo servidor:

```bash
# Fazer upload dos arquivos para o servidor
# Navegar até o diretório do projeto
cd /var/www/sem-fronteiras-ssh

# Executar setup automático (como root)
sudo ./scripts/setup-servidor.sh
```

### Execução Manual de Scripts

Para executar qualquer script individualmente:

```bash
cd scripts
chmod +x nome-do-script.sh
./nome-do-script.sh
```

### Verificar Status dos Backups

Para verificar se todos os backups estão funcionando corretamente:

```bash
cd scripts
chmod +x verificar-backups.sh
./verificar-backups.sh
```

### Instalar Serviço Systemd (apenas no servidor)

Para instalar o serviço de inicialização automática no servidor:

```bash
# Copiar o arquivo para o diretório systemd
sudo cp scripts/sem-fronteiras.service /etc/systemd/system/

# Recarregar e habilitar o serviço
sudo systemctl daemon-reload
sudo systemctl enable sem-fronteiras.service
```

## Observações

- Certifique-se de ter as permissões necessárias antes de executar os scripts
- Alguns scripts podem requerer privilégios de administrador
- Sempre revise o conteúdo dos scripts antes da execução em produção
